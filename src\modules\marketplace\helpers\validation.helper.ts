import { Injectable } from '@nestjs/common';
import { Product } from '../entities';
import { ProductStatus, ProductCategory } from '../enums';
import { AppException } from '@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '../exceptions';

/**
 * Helper class cho việc xác thực dữ liệu trong module marketplace
 */
@Injectable()
export class ValidationHelper {
  /**
   * Map chứa các thông báo lỗi dựa trên trạng thái sản phẩm
   * Sử dụng map thay vì switch để dễ bảo trì và mở rộng
   */
  private readonly errorMessageStatus = {
    [ProductStatus.DRAFT]: 'Sản phẩm đang ở trạng thái bản nháp, chưa được đăng bán',
    [ProductStatus.PENDING]: 'Sản phẩm đang chờ duyệt, chưa được đăng bán',
    [ProductStatus.REJECTED]: 'Sản phẩm đã bị từ chối, không được đăng bán',
    [ProductStatus.DELETED]: 'Sản phẩm đã bị xóa, không còn tồn tại',
    [ProductStatus.APPROVED]: 'Sản phẩm đã được đăng bán'
  };

  /**
   * Kiểm tra sản phẩm có tồn tại không
   * @param product Sản phẩm cần kiểm tra
   * @throws AppException nếu sản phẩm không tồn tại
   */
  private validateProductExists(product: Product | null): asserts product is Product {
    if (!product) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND,
        'Sản phẩm không tồn tại'
      );
    }
  }

  /**
   * Kiểm tra sản phẩm không bị xóa
   * @param product Sản phẩm cần kiểm tra
   * @throws AppException nếu sản phẩm đã bị xóa
   */
  validateProductNotDeleted(product: Product | null): void {
    this.validateProductExists(product);

    if (product.status === ProductStatus.DELETED) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_DELETED,
        this.errorMessageStatus[ProductStatus.DELETED]
      );
    }
  }

  /**
   * Kiểm tra sản phẩm có thuộc về người dùng không
   * @param product Sản phẩm cần kiểm tra
   * @param userId ID người dùng
   * @throws AppException nếu sản phẩm không thuộc về người dùng
   */
  validateProductOwnership(product: Product | null, userId: number): void {
    this.validateProductExists(product);
    this.validateProductNotDeleted(product);

    if (product.userId !== userId) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.UNAUTHORIZED,
        'Bạn không có quyền truy cập sản phẩm này'
      );
    }
  }

  /**
   * Kiểm tra sản phẩm có thuộc về nhân viên không
   * @param product Sản phẩm cần kiểm tra
   * @param employeeId ID nhân viên
   * @throws AppException nếu sản phẩm không thuộc về nhân viên
   */
  validateProductEmployeeOwnership(product: Product | null, employeeId: number): void {
    this.validateProductExists(product);
    this.validateProductNotDeleted(product);

    if (product.employeeId !== employeeId) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.UNAUTHORIZED,
        'Bạn không có quyền truy cập sản phẩm này'
      );
    }
  }

  /**
   * Kiểm tra sản phẩm có ở trạng thái hợp lệ để cập nhật không
   * @param product Sản phẩm cần kiểm tra
   * @throws AppException nếu sản phẩm không ở trạng thái hợp lệ
   */
  validateProductIsDraft(product: Product | null): void {
    this.validateProductExists(product);
    this.validateProductNotDeleted(product);

    // Nếu là sản phẩm của admin, có thể cập nhật ở trạng thái DRAFT hoặc APPROVED
    if (product.employeeId && !product.userId) {
      if (product.status !== ProductStatus.DRAFT && product.status !== ProductStatus.APPROVED) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.INVALID_STATUS,
          'Chỉ có thể cập nhật sản phẩm ở trạng thái DRAFT hoặc APPROVED'
        );
      }
    }
    // Nếu là sản phẩm của user, chỉ có thể cập nhật ở trạng thái DRAFT
    else {
      if (product.status !== ProductStatus.DRAFT) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.INVALID_STATUS,
          this.errorMessageStatus[product.status] || 'Chỉ có thể cập nhật sản phẩm ở trạng thái DRAFT'
        );
      }
    }
  }

  /**
   * Kiểm tra sản phẩm có đủ thông tin để gửi duyệt không
   * @param product Sản phẩm cần kiểm tra
   * @throws AppException nếu sản phẩm thiếu thông tin
   */
  validateProductForSubmission(product: Product | null): void {
    this.validateProductExists(product);
    this.validateProductNotDeleted(product);

    const requiredFields: string[] = [];

    if (!product.name) requiredFields.push('tên sản phẩm');
    if (!product.images || product.images.length === 0) requiredFields.push('ảnh sản phẩm');
    if (product.listedPrice === undefined || product.listedPrice === null) requiredFields.push('giá niêm yết');
    if (product.discountedPrice === undefined || product.discountedPrice === null) requiredFields.push('giá sau giảm');
    if (product.category === undefined || product.category === null) requiredFields.push('loại sản phẩm');

    if (requiredFields.length > 0) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.MISSING_REQUIRED_FIELDS,
        `Sản phẩm thiếu các trường bắt buộc: ${requiredFields.join(', ')}`
      );
    }

    // Kiểm tra giá
    this.validateProductPrice(product.listedPrice, product.discountedPrice);
  }

  /**
   * Kiểm tra sản phẩm có hợp lệ để thêm vào giỏ hàng không
   * @param product Sản phẩm cần kiểm tra
   * @param userId ID người dùng
   * @throws AppException nếu sản phẩm không hợp lệ
   */
  validateProductForCart(product: Product | null, userId: number): void {
    this.validateProductExists(product);
    this.validateProductNotDeleted(product);

    if (product.status !== ProductStatus.APPROVED) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.PRODUCT_NOT_APPROVED,
        this.errorMessageStatus[product.status] || 'Chỉ có thể thêm sản phẩm đã được phê duyệt vào giỏ hàng'
      );
    }

    if (product.userId === userId) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.CANNOT_BUY_OWN_PRODUCT,
        'Không thể mua sản phẩm của chính mình'
      );
    }
  }

  /**
   * Kiểm tra sản phẩm có ở trạng thái PENDING không
   * @param product Sản phẩm cần kiểm tra
   * @throws AppException nếu sản phẩm không ở trạng thái PENDING
   */
  validateProductIsPending(product: Product | null): void {
    this.validateProductExists(product);
    this.validateProductNotDeleted(product);

    if (product.status !== ProductStatus.PENDING) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.INVALID_STATUS,
        `Chỉ có thể hủy gửi duyệt sản phẩm ở trạng thái PENDING. Sản phẩm hiện tại đang ở trạng thái ${product.status}.`
      );
    }
  }

  /**
   * Kiểm tra quy tắc chuyển trạng thái sản phẩm
   * @param product Sản phẩm cần kiểm tra
   * @param currentEmployeeId ID của nhân viên hiện tại
   * @param newStatus Trạng thái mới
   * @throws AppException nếu không thể chuyển trạng thái
   */
  validateProductStatusTransition(
    product: Product | null,
    currentEmployeeId: number,
    newStatus: ProductStatus
  ): void {
    this.validateProductExists(product);
    this.validateProductNotDeleted(product);

    const isOwner = product.employeeId === currentEmployeeId;
    const currentStatus = product.status;

    // Kiểm tra quy tắc chuyển trạng thái cho sản phẩm của người dùng
    if (product.userId && !isOwner) {
      // Admin thao tác với sản phẩm của user
      switch (currentStatus) {
        case ProductStatus.DRAFT:
          // DRAFT -> Không làm được gì
          throw new AppException(
            MARKETPLACE_ERROR_CODES.INVALID_STATUS_TRANSITION,
            'Không thể thay đổi trạng thái của sản phẩm DRAFT của người dùng'
          );

        case ProductStatus.PENDING:
          // PENDING -> APPROVE hoặc REJECT
          if (newStatus !== ProductStatus.APPROVED && newStatus !== ProductStatus.REJECTED) {
            throw new AppException(
              MARKETPLACE_ERROR_CODES.INVALID_STATUS_TRANSITION,
              'Sản phẩm PENDING chỉ có thể chuyển sang APPROVED hoặc REJECTED'
            );
          }
          break;

        case ProductStatus.APPROVED:
          // APPROVED -> Không làm được gì
          throw new AppException(
            MARKETPLACE_ERROR_CODES.INVALID_STATUS_TRANSITION,
            'Không thể thay đổi trạng thái của sản phẩm APPROVED của người dùng'
          );

        case ProductStatus.REJECTED:
          // REJECTED -> Không làm được gì
          throw new AppException(
            MARKETPLACE_ERROR_CODES.INVALID_STATUS_TRANSITION,
            'Không thể thay đổi trạng thái của sản phẩm REJECTED của người dùng'
          );

        default:
          throw new AppException(
            MARKETPLACE_ERROR_CODES.INVALID_STATUS_TRANSITION,
            'Không thể thay đổi trạng thái của sản phẩm này'
          );
      }
    }
    // Kiểm tra quy tắc chuyển trạng thái cho sản phẩm của admin
    else if (isOwner) {
      // Admin thao tác với sản phẩm của chính mình
      switch (currentStatus) {
        case ProductStatus.DRAFT:
          // DRAFT -> APPROVE hoặc DELETE
          if (newStatus !== ProductStatus.APPROVED && newStatus !== ProductStatus.DELETED) {
            throw new AppException(
              MARKETPLACE_ERROR_CODES.INVALID_STATUS_TRANSITION,
              'Sản phẩm DRAFT chỉ có thể chuyển sang APPROVED hoặc DELETED'
            );
          }
          break;

        case ProductStatus.APPROVED:
          // APPROVED -> DRAFT hoặc DELETE
          if (newStatus !== ProductStatus.DRAFT && newStatus !== ProductStatus.DELETED) {
            throw new AppException(
              MARKETPLACE_ERROR_CODES.INVALID_STATUS_TRANSITION,
              'Sản phẩm APPROVED chỉ có thể chuyển sang DRAFT hoặc DELETED'
            );
          }
          break;

        case ProductStatus.DELETED:
          // DELETED -> Không làm được gì
          throw new AppException(
            MARKETPLACE_ERROR_CODES.INVALID_STATUS_TRANSITION,
            'Không thể thay đổi trạng thái của sản phẩm đã xóa'
          );

        default:
          throw new AppException(
            MARKETPLACE_ERROR_CODES.INVALID_STATUS_TRANSITION,
            'Không thể thay đổi trạng thái của sản phẩm này'
          );
      }
    } else {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.UNAUTHORIZED,
        'Bạn không có quyền cập nhật trạng thái sản phẩm này'
      );
    }
  }

  /**
   * Kiểm tra sản phẩm có đủ điều kiện để đăng bán không
   * @param product Sản phẩm cần kiểm tra
   * @param employeeId ID của nhân viên
   * @throws AppException nếu sản phẩm không đủ điều kiện
   */
  validateProductForPublish(product: Product | null, employeeId: number): void {
    this.validateProductExists(product);
    this.validateProductNotDeleted(product);

    // Kiểm tra quyền sở hữu
    if (product.employeeId !== employeeId) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.UNAUTHORIZED,
        'Bạn không có quyền đăng bán sản phẩm này'
      );
    }

    // Kiểm tra trạng thái
    if (product.status !== ProductStatus.DRAFT) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.INVALID_STATUS,
        'Chỉ có thể đăng bán sản phẩm ở trạng thái DRAFT'
      );
    }

    // Kiểm tra các trường bắt buộc
    const requiredFields: string[] = [];

    if (!product.name) requiredFields.push('tên sản phẩm');
    if (!product.images || product.images.length === 0) requiredFields.push('ảnh sản phẩm');
    if (product.listedPrice === undefined || product.listedPrice === null) requiredFields.push('giá niêm yết');
    if (product.discountedPrice === undefined || product.discountedPrice === null) requiredFields.push('giá sau giảm');
    if (product.category === undefined || product.category === null) requiredFields.push('loại sản phẩm');

    if (requiredFields.length > 0) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.MISSING_REQUIRED_FIELDS,
        `Thiếu các trường bắt buộc: ${requiredFields.join(', ')}`
      );
    }

    // Kiểm tra giá
    this.validateProductPrice(product.listedPrice, product.discountedPrice);
  }

  /**
   * Kiểm tra giá sản phẩm hợp lệ
   * @param listedPrice Giá niêm yết
   * @param discountedPrice Giá sau giảm
   * @throws AppException nếu giá không hợp lệ
   */
  validateProductPrice(listedPrice: number, discountedPrice: number): void {
    if (listedPrice < discountedPrice) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.INVALID_PRICE,
        'Giá niêm yết phải lớn hơn hoặc bằng giá sau giảm'
      );
    }
  }

  /**
   * Kiểm tra quy tắc xóa sản phẩm
   * @param product Sản phẩm cần kiểm tra
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @throws AppException nếu không thể xóa sản phẩm
   */
  validateProductDeletionRules(product: Product | null, employeeId: number): void {
    this.validateProductExists(product);

    const isOwner = product.employeeId === employeeId;

    // Nếu là sản phẩm của admin
    if (isOwner) {
      // Admin có thể xóa sản phẩm ở trạng thái DRAFT hoặc APPROVED
      if (product.status !== ProductStatus.DRAFT && product.status !== ProductStatus.APPROVED) {
        throw new AppException(
          MARKETPLACE_ERROR_CODES.PRODUCT_DELETE_NOT_ALLOWED,
          `Chỉ có thể xóa sản phẩm ở trạng thái DRAFT hoặc APPROVED. Sản phẩm hiện tại đang ở trạng thái ${product.status}.`
        );
      }
    }
    // Nếu là sản phẩm của user
    else if (product.userId) {
      // Admin không thể xóa sản phẩm của user
      throw new AppException(
        MARKETPLACE_ERROR_CODES.UNAUTHORIZED,
        'Bạn không có quyền xóa sản phẩm của người dùng'
      );
    } else {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.UNAUTHORIZED,
        'Bạn không có quyền xóa sản phẩm này'
      );
    }
  }

  /**
   * Kiểm tra sản phẩm có thể được đăng bán không (không phải sản phẩm đã mua)
   * @param sourceId ID của tài nguyên gốc
   * @throws AppException nếu không thể đăng bán sản phẩm đã mua
   */
  validateCanSellProduct(sourceId: string | null): void {
    if (sourceId) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.CANNOT_SELL_PURCHASED_PRODUCT,
        'Không thể đăng bán sản phẩm đã mua từ người khác. Chỉ có thể đăng bán tài nguyên do bạn tự tạo.'
      );
    }
  }

  /**
   * Kiểm tra tài nguyên có thể được tạo thành sản phẩm để bán không
   * @param resourceId ID của tài nguyên
   * @param userId ID của người dùng
   * @param isOwner Người dùng có phải chủ sở hữu tài nguyên không
   * @throws AppException nếu không thể tạo sản phẩm từ tài nguyên này
   */
  validateResourceCanBeProductized(resourceId: string, userId: number, isOwner: boolean): void {
    if (!isOwner) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.CANNOT_SELL_PURCHASED_PRODUCT,
        'Chỉ có thể đăng bán tài nguyên do bạn tự tạo. Tài nguyên này được chia sẻ từ người khác.'
      );
    }
  }

  /**
   * Kiểm tra tài nguyên gốc có trạng thái APPROVED không khi tạo sản phẩm
   * @param sourceId ID của tài nguyên gốc
   * @param category Loại sản phẩm
   * @param repositories Object chứa các repository cần thiết
   * @throws AppException nếu tài nguyên gốc không có trạng thái APPROVED
   */
  async validateSourceResourceStatus(
    sourceId: string | null,
    category: ProductCategory,
    repositories: {
      knowledgeFileRepository?: any;
      agentRepository?: any;
      userDataFineTuneRepository?: any;
      adminDataFineTuneRepository?: any;
      strategyRepository?: any;
    }
  ): Promise<void> {
    // Nếu không có sourceId thì bỏ qua validation
    if (!sourceId) {
      return;
    }

    switch (category) {
      case ProductCategory.KNOWLEDGE_FILE:
        await this.validateKnowledgeFileStatus(sourceId, repositories.knowledgeFileRepository);
        break;

      case ProductCategory.AGENT:
        await this.validateAgentStatus(sourceId, repositories.agentRepository);
        break;

      case ProductCategory.FINETUNE:
        await this.validateFineTuneDataStatus(sourceId, repositories.userDataFineTuneRepository, repositories.adminDataFineTuneRepository);
        break;

      case ProductCategory.STRATEGY:
        await this.validateStrategyStatus(sourceId, repositories.strategyRepository);
        break;

      default:
        throw new AppException(
          MARKETPLACE_ERROR_CODES.INVALID_CATEGORY,
          `Loại sản phẩm không được hỗ trợ: ${category}`
        );
    }
  }

  /**
   * Kiểm tra Knowledge File có trạng thái APPROVED không
   */
  private async validateKnowledgeFileStatus(sourceId: string, repository: any): Promise<void> {
    if (!repository) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.GENERAL_ERROR,
        'Repository không được cung cấp để kiểm tra Knowledge File'
      );
    }

    const knowledgeFile = await repository.findOne({
      where: { id: sourceId },
      select: ['id', 'status']
    });

    if (!knowledgeFile) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.RESOURCE_NOT_FOUND,
        `Không tìm thấy Knowledge File với ID: ${sourceId}`
      );
    }

    if (knowledgeFile.status !== 'APPROVED') {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.RESOURCE_NOT_APPROVED,
        `Knowledge File phải có trạng thái APPROVED để có thể tạo sản phẩm. Trạng thái hiện tại: ${knowledgeFile.status}`
      );
    }
  }

  /**
   * Kiểm tra Agent có trạng thái APPROVED không
   */
  private async validateAgentStatus(sourceId: string, repository: any): Promise<void> {
    if (!repository) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.GENERAL_ERROR,
        'Repository không được cung cấp để kiểm tra Agent'
      );
    }

    const agent = await repository.findOne({
      where: { id: sourceId },
      select: ['id', 'status']
    });

    if (!agent) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.RESOURCE_NOT_FOUND,
        `Không tìm thấy Agent với ID: ${sourceId}`
      );
    }

    if (agent.status !== 'APPROVED') {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.RESOURCE_NOT_APPROVED,
        `Agent phải có trạng thái APPROVED để có thể tạo sản phẩm. Trạng thái hiện tại: ${agent.status}`
      );
    }
  }

  /**
   * Kiểm tra Fine Tune Data có trạng thái APPROVED không
   */
  private async validateFineTuneDataStatus(sourceId: string, userRepository: any, adminRepository: any): Promise<void> {
    if (!userRepository || !adminRepository) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.GENERAL_ERROR,
        'Repository không được cung cấp để kiểm tra Fine Tune Data'
      );
    }

    // Kiểm tra trong user_data_fine_tune trước
    let fineTuneData = await userRepository.findOne({
      where: { id: sourceId },
      select: ['id', 'status']
    });

    // Nếu không tìm thấy trong user, kiểm tra trong admin
    if (!fineTuneData) {
      fineTuneData = await adminRepository.findOne({
        where: { id: sourceId },
        select: ['id', 'status']
      });
    }

    if (!fineTuneData) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.RESOURCE_NOT_FOUND,
        `Không tìm thấy Fine Tune Data với ID: ${sourceId}`
      );
    }

    if (fineTuneData.status !== 'APPROVED') {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.RESOURCE_NOT_APPROVED,
        `Fine Tune Data phải có trạng thái APPROVED để có thể tạo sản phẩm. Trạng thái hiện tại: ${fineTuneData.status}`
      );
    }
  }

  /**
   * Kiểm tra Strategy có trạng thái APPROVED không
   */
  private async validateStrategyStatus(sourceId: string, repository: any): Promise<void> {
    if (!repository) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.GENERAL_ERROR,
        'Repository không được cung cấp để kiểm tra Strategy'
      );
    }

    const strategy = await repository.findOne({
      where: { id: sourceId },
      select: ['id', 'status']
    });

    if (!strategy) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.RESOURCE_NOT_FOUND,
        `Không tìm thấy Strategy với ID: ${sourceId}`
      );
    }

    if (strategy.status !== 'APPROVED') {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.RESOURCE_NOT_APPROVED,
        `Strategy phải có trạng thái APPROVED để có thể tạo sản phẩm. Trạng thái hiện tại: ${strategy.status}`
      );
    }
  }
}