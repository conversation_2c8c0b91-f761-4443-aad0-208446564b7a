import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';
import { ProviderEnumq } from '@/shared/services/ai/utils/type-provider.util';

/**
 * DTO cho việc cập nhật model registry
 */
export class UpdateModelRegistryDto {
  /**
   * Nhà cung cấp model
   */
  @ApiPropertyOptional({
    description: 'Nhà cung cấp model',
    enum: ProviderEnumq,
    example: ProviderEnumq.OPENAI,
  })
  @IsOptional()
  @IsEnum(ProviderEnumq)
  provider?: ProviderEnumq;

  /**
   * Tên mẫu đại diện của model
   */
  @ApiPropertyOptional({
    description: 'Tên mẫu đại diện của model',
    example: 'gpt-4*',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  modelNamePattern?: string;

  /**
   * <PERSON><PERSON><PERSON> loại dữ liệu đầu vào hỗ trợ (text, image, audio,...)
   */
  @ApiPropertyOptional({
    description: '<PERSON>ác loại dữ liệu đầu vào hỗ trợ',
    example: ['text', 'image'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  inputModalities?: string[];

  /**
   * Các loại dữ liệu đầu ra hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu ra hỗ trợ',
    example: ['text'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  outputModalities?: string[];

  /**
   * Các tham số sampling như temperature, top_p,...
   */
  @ApiPropertyOptional({
    description: 'Các tham số sampling như temperature, top_p,...',
    example: [
      { name: 'temperature', type: 'number', min: 0, max: 2, default: 1 },
      { name: 'top_p', type: 'number', min: 0, max: 1, default: 1 }
    ],
    type: [Object],
  })
  @IsOptional()
  @IsArray()
  samplingParameters?: any[];

  /**
   * Tập hợp feature đặc biệt (như tool-use, function-calling)
   */
  @ApiPropertyOptional({
    description: 'Tập hợp feature đặc biệt',
    example: ['tool-use', 'function-calling', 'vision'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  features?: string[];
}
