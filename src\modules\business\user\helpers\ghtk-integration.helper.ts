import { Injectable, Logger } from '@nestjs/common';
import { GHTKShipmentService } from '@modules/business/user/services';
import { UserOrder } from '@modules/business/entities';
import { UserProduct } from '@modules/business/entities';
import { UserConvertCustomer } from '@modules/business/entities';
import { IGHTKCreateOrderRequest, IGHTKProduct, IGHTKOrder } from '@modules/business/interfaces';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '../../exceptions';

/**
 * Helper để tích hợp GHTK với hệ thống đơn hàng
 */
@Injectable()
export class GHTKIntegrationHelper {
  private readonly logger = new Logger(GHTKIntegrationHelper.name);

  constructor(private readonly ghtkService: GHTKShipmentService) {}

  /**
   * Chuyển đổi đơn hàng hệ thống sang format GHTK
   * @param order Đơn hàng hệ thống
   * @param customer Thông tin khách hàng
   * @param products Danh sách sản phẩm
   * @param pickupInfo Thông tin địa chỉ lấy hàng
   * @returns Request data cho GHTK API
   */
  convertOrderToGHTKFormat(
    order: UserOrder,
    customer: UserConvertCustomer,
    products: UserProduct[],
    pickupInfo: {
      name: string;
      address: string;
      province: string;
      district: string;
      ward: string;
      tel: string;
    }
  ): IGHTKCreateOrderRequest {
    try {
      // Chuyển đổi sản phẩm
      const ghtkProducts: IGHTKProduct[] = this.convertProductsToGHTKFormat(order, products);

      // Chuyển đổi thông tin đơn hàng
      const ghtkOrder: IGHTKOrder = this.convertOrderInfoToGHTKFormat(
        order,
        customer,
        pickupInfo
      );

      return {
        products: ghtkProducts,
        order: ghtkOrder
      };
    } catch (error) {
      this.logger.error('Lỗi khi chuyển đổi đơn hàng sang format GHTK:', error);
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_CONFIG_ERROR,
        `Lỗi khi chuyển đổi đơn hàng: ${error.message}`
      );
    }
  }

  /**
   * Chuyển đổi danh sách sản phẩm sang format GHTK
   */
  private convertProductsToGHTKFormat(order: UserOrder, products: UserProduct[]): IGHTKProduct[] {
    const productInfo = order.productInfo as any;
    const orderProducts = productInfo?.products || [];

    if (!Array.isArray(orderProducts)) {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_CONFIG_ERROR,
        'Thông tin sản phẩm trong đơn hàng không hợp lệ'
      );
    }

    return orderProducts.map((orderProduct: any) => {
      const product = products.find(p => p.id === orderProduct.productId);

      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${orderProduct.productId}`
        );
      }

      // Lấy thông tin cân nặng từ shipmentConfig
      const shipmentConfig = product.shipmentConfig as any;
      const weight = shipmentConfig?.weightGram
        ? shipmentConfig.weightGram / 1000 // Chuyển từ gram sang kg
        : 0.5; // Mặc định 0.5kg nếu không có thông tin

      return {
        name: orderProduct.name || product.name,
        price: orderProduct.unitPrice || 0,
        weight: weight,
        quantity: orderProduct.quantity || 1,
        product_code: product.id.toString() // Sử dụng ID sản phẩm làm product_code
      };
    });
  }

  /**
   * Chuyển đổi thông tin đơn hàng sang format GHTK
   */
  private convertOrderInfoToGHTKFormat(
    order: UserOrder,
    customer: UserConvertCustomer,
    pickupInfo: any
  ): IGHTKOrder {
    // Parse địa chỉ giao hàng
    const logisticInfo = order.logisticInfo as any;
    const deliveryAddress = this.parseDeliveryAddress(
      (logisticInfo?.deliveryAddress as string) || customer.address || ''
    );

    // Tính tổng giá trị đơn hàng
    const orderValue = this.calculateOrderValue(order);

    // Parse bill info
    const billInfo = order.billInfo as any;
    const pickMoney = typeof billInfo?.total === 'number' ? billInfo.total : 0;

    // Parse shipping note
    const shippingNote = typeof logisticInfo?.shippingNote === 'string' ? logisticInfo.shippingNote : '';

    return {
      id: `ORDER_${order.id}_${Date.now()}`, // Tạo ID unique cho GHTK

      // Thông tin lấy hàng
      pick_name: pickupInfo.name,
      pick_address: pickupInfo.address,
      pick_province: pickupInfo.province,
      pick_district: pickupInfo.district,
      pick_ward: pickupInfo.ward,
      pick_tel: pickupInfo.tel,

      // Thông tin giao hàng
      name: customer.name,
      address: deliveryAddress.address,
      province: deliveryAddress.province,
      district: deliveryAddress.district,
      ward: deliveryAddress.ward,
      hamlet: deliveryAddress.hamlet || 'Khác',
      tel: customer.phone,

      // Thông tin đơn hàng
      value: orderValue,
      pick_money: pickMoney, // COD amount
      note: shippingNote,
      transport: 'road', // Mặc định đường bộ
      pick_option: 'cod', // Mặc định thu hộ
      deliver_option: 'none', // Không sử dụng xTeam
      is_freeship: '0' // Mặc định không freeship
    };
  }

  /**
   * Parse địa chỉ giao hàng từ string
   */
  private parseDeliveryAddress(addressString: string): {
    address: string;
    province: string;
    district: string;
    ward: string;
    hamlet?: string;
  } {
    // Tách địa chỉ theo dấu phẩy
    const parts = addressString.split(',').map(part => part.trim());
    
    if (parts.length < 3) {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHTK_INVALID_ADDRESS,
        'Địa chỉ giao hàng không đủ thông tin (cần ít nhất: địa chỉ, quận/huyện, tỉnh/thành)'
      );
    }

    return {
      address: parts[0] || '',
      ward: parts[1] || '',
      district: parts[2] || '',
      province: parts[3] || parts[2], // Nếu không có ward thì district là parts[1], province là parts[2]
      hamlet: 'Khác'
    };
  }

  /**
   * Tính tổng giá trị đơn hàng
   */
  private calculateOrderValue(order: UserOrder): number {
    const productInfo = order.productInfo as any;
    const products = productInfo?.products || [];

    if (!Array.isArray(products)) {
      return 0;
    }

    return products.reduce((total: number, product: any) => {
      return total + (product.totalPrice || 0);
    }, 0);
  }





  /**
   * Tính tổng trọng lượng đơn hàng
   */
  private calculateTotalWeight(order: UserOrder, products: UserProduct[]): number {
    const productInfo = order.productInfo as any;
    const orderProducts = productInfo?.products || [];

    if (!Array.isArray(orderProducts)) {
      return 0.5; // Default weight
    }

    return orderProducts.reduce((totalWeight: number, orderProduct: any) => {
      const product = products.find(p => p.id === orderProduct.productId);

      if (!product) return totalWeight;

      const shipmentConfig = product.shipmentConfig as any;
      const productWeight = shipmentConfig?.weightGram
        ? shipmentConfig.weightGram / 1000 // Chuyển từ gram sang kg
        : 0.5; // Mặc định 0.5kg

      return totalWeight + (productWeight * (orderProduct.quantity || 1));
    }, 0);
  }
}
