import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';
import { DataFineTuneStatus } from '@/modules/models/constants/data-fine-tune-status.enum';

export enum AdminDataFineTuneSortBy {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  NAME = 'name'
}

/**
 * DTO query cho admin data fine-tune
 */
export class AdminDataFineTuneQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Tìm kiếm theo trạng thái',
    enum: DataFineTuneStatus,
    example: DataFineTuneStatus.PENDING,
    default: DataFineTuneStatus.PENDING,
    required: false
  })
  @IsOptional()
  @IsString()
  status?: DataFineTuneStatus;

  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: AdminDataFineTuneSortBy,
    example: AdminDataFineTuneSortBy.CREATED_AT,
    default: AdminDataFineTuneSortBy.CREATED_AT,
    required: false
  })
  @IsOptional()
  @IsString()
  sortBy?: AdminDataFineTuneSortBy = AdminDataFineTuneSortBy.CREATED_AT;

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
    default: SortDirection.DESC,
    required: false
  })
  @IsOptional()
  @IsString()
  sortDirection?: SortDirection = SortDirection.DESC;
}
