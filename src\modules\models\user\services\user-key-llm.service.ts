import { AiProviderHelper } from '@/shared/services/ai/helpers/ai-provider.helper';
import { AppException } from '@common/exceptions';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { MODELS_ERROR_CODES } from '../../exceptions';
import { ApiKeyEncryptionHelper } from '../../helpers/api-key-encryption.helper';
import { UserKeyLlmRepository } from '../../repositories/user-key-llm.repository';
import {
  CreateUserKeyLlmDto,
  TestConnectionResponseDto,
  UpdateUserKeyLlmDto,
  UserKeyLlmQueryDto,
  UserKeyLlmResponseDto
} from '../dto/user-key-llm';
import { UserKeyLlmMapper } from '../mappers/user-key-llm.mapper';

/**
 * Service xử lý business logic cho User Key LLM
 */
@Injectable()
export class UserKeyLlmService {
  private readonly logger = new Logger(UserKeyLlmService.name);

  constructor(
    private readonly userKeyLlmRepository: UserKeyLlmRepository,
    private readonly apiKeyEncryptionHelper: ApiKeyEncryptionHelper,
    private readonly aiProviderService: AiProviderHelper,
  ) { }

  /**
   * Tạo mới user key LLM
   */
  @Transactional()
  async create(userId: number, createDto: CreateUserKeyLlmDto): Promise<ApiResponseDto<{ id: string, error?: string }>> {
    this.logger.log(`Creating user key LLM for user ${userId}`);

    // Validate các fields
    if (!UserKeyLlmMapper.validateKeyName(createDto.name)) {
      throw new AppException(MODELS_ERROR_CODES.USER_KEY_LLM_INVALID_NAME);
    }

    // Kiểm tra trùng tên
    const existsByName = await this.userKeyLlmRepository.existsByNameAndUserId(createDto.name, userId);
    if (existsByName) {
      throw new AppException(MODELS_ERROR_CODES.USER_KEY_LLM_NAME_EXISTS);
    }

    // Test connection với provider
    const testResult = await this.aiProviderService.testConnection(
      createDto.apiKey,
      createDto.provider,
      false,
      userId
    );

    if (!testResult.success) {
      throw new AppException(MODELS_ERROR_CODES.USER_KEY_LLM_CONNECTION_FAILED, testResult.error);
    }

    // Mã hóa API key
    const encryptedApiKey = this.apiKeyEncryptionHelper.encryptUserApiKey(createDto.apiKey, userId);

    // Tạo entity mới
    const userKeyLlm = this.userKeyLlmRepository.create({
      name: createDto.name,
      provider: createDto.provider,
      apiKey: encryptedApiKey,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    // Lưu vào database
    const savedUserKeyLlm = await this.userKeyLlmRepository.save(userKeyLlm);

    this.logger.log(`Created user key LLM ${userKeyLlm.id} successfully`);
    return ApiResponseDto.success({ id: savedUserKeyLlm.id, error: testResult.error });
  }

  /**
   * Lấy danh sách user key LLM có phân trang
   */
  async findAll(userId: number, queryDto: UserKeyLlmQueryDto): Promise<ApiResponseDto<PaginatedResult<UserKeyLlmResponseDto>>> {
    this.logger.log(`Getting user key LLM list for user ${userId}`);

    // Lấy dữ liệu từ repository với phân trang
    const result = await this.userKeyLlmRepository.findWithPagination(queryDto, userId);

    // Convert sang DTO với masked API key
    const items = UserKeyLlmMapper.toResponseDtoArray(result.items);

    return ApiResponseDto.paginated({
      items,
      meta: result.meta
    });
  }

  /**
   * Cập nhật user key LLM
   */
  @Transactional()
  async update(userId: number, id: string, updateDto: UpdateUserKeyLlmDto): Promise<ApiResponseDto<{ id: string, error?: string }>> {
    this.logger.log(`Updating user key LLM ${id} for user ${userId}`);

    // Tìm user key LLM hiện tại
    const existingUserKeyLlm = await this.userKeyLlmRepository.findByIdAndUserId(id, userId);
    if (!existingUserKeyLlm) {
      throw new AppException(MODELS_ERROR_CODES.USER_KEY_LLM_NOT_FOUND);
    }

    // Validate các fields nếu có thay đổi
    if (updateDto.name && !UserKeyLlmMapper.validateKeyName(updateDto.name)) {
      throw new AppException(MODELS_ERROR_CODES.USER_KEY_LLM_INVALID_NAME);
    }

    // Kiểm tra trùng tên (nếu có thay đổi tên)
    if (updateDto.name && updateDto.name !== existingUserKeyLlm.name) {
      const existsByName = await this.userKeyLlmRepository.existsByNameAndUserId(updateDto.name, userId, id);
      if (existsByName) {
        throw new AppException(MODELS_ERROR_CODES.USER_KEY_LLM_NAME_EXISTS);
      }
    }

    // Test connection nếu có thay đổi API key
    let testResult: TestConnectionResponseDto | undefined;
    if (updateDto.apiKey) {
      testResult = await this.testApiKeyConnection(updateDto.apiKey, existingUserKeyLlm.provider, userId);

      if (!testResult.success) {
        return ApiResponseDto.success({ id: existingUserKeyLlm.id, error: testResult?.error });
      }
    }

    // Cập nhật các trường
    if (updateDto.name !== undefined) {
      existingUserKeyLlm.name = updateDto.name;
    }

    if (updateDto.apiKey !== undefined) {
      existingUserKeyLlm.apiKey = this.apiKeyEncryptionHelper.encryptUserApiKey(updateDto.apiKey, userId);
    }

    existingUserKeyLlm.updatedAt = Date.now();

    // Lưu thay đổi
    await this.userKeyLlmRepository.save(existingUserKeyLlm);

    this.logger.log(`Updated user key LLM ${id} successfully`);
    return ApiResponseDto.success({ id: existingUserKeyLlm.id });
  }

  /**
   * Xóa user key LLM (soft delete)
   */
  @Transactional()
  async remove(userId: number, id: string): Promise<ApiResponseDto<{ message: string }>> {
    this.logger.log(`Removing user key LLM ${id} for user ${userId}`);

    // Kiểm tra user key LLM tồn tại
    const existingUserKeyLlm = await this.userKeyLlmRepository.findByIdAndUserId(id, userId);
    if (!existingUserKeyLlm) {
      throw new AppException(MODELS_ERROR_CODES.USER_KEY_LLM_NOT_FOUND);
    }

    // Thực hiện soft delete
    const deleted = await this.userKeyLlmRepository.softDeleteUserKeyLlm(id, userId);
    if (!deleted) {
      throw new AppException(MODELS_ERROR_CODES.USER_KEY_LLM_DELETE_FAILED);
    }

    this.logger.log(`Soft deleted user key LLM ${id} successfully`);
    return ApiResponseDto.success({ message: 'Xóa user key LLM thành công' });
  }

  /**
   * Test connection với API key (private method)
   * @param apiKey API key thô
   * @param provider Provider
   * @param baseUrl Base URL (optional)
   * @returns Test connection result
   */
  private async testApiKeyConnection(
    apiKey: string,
    provider: string,
    userId: number
  ): Promise<TestConnectionResponseDto> {
    try {
      const testResult = await this.aiProviderService.testConnection(apiKey, provider, false, userId);

      if (testResult.success) {
        return UserKeyLlmMapper.toTestConnectionSuccessDto(
          testResult.responseTime || 0,
        );
      } else {
        return UserKeyLlmMapper.toTestConnectionFailureDto(testResult.error || 'Connection failed');
      }
    } catch (error) {
      this.logger.error(`Test connection failed: ${error.message}`, error.stack);
      return UserKeyLlmMapper.toTestConnectionFailureDto(error.message || 'Unknown error');
    }
  }
}
