import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty } from 'class-validator';
import { Transform } from 'class-transformer';

/**
 * DTO cho việc xóa nhiều sản phẩm khỏi giỏ hàng
 */
export class RemoveMultipleCartItemsDto {
  @ApiProperty({
    description: 'Danh sách ID của các cart item cần xóa',
    type: [Number],
    example: [1, 2, 3],
  })
  @IsArray()
  @IsNotEmpty()
  @Transform(({ value }) => {
    if (Array.isArray(value)) {
      return value.map(v => typeof v === 'string' ? parseInt(v, 10) : v);
    }
    return value;
  })
  cartItemIds: number[];
}
