import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { EmployeeModule } from '@modules/employee/employee.module';
import { ServicesModule } from '@shared/services/services.module';

// Import entities
import * as entities from '../entities';

// Import repositories
import * as repositories from '../repositories';

// Import controllers
import {
  AdminModelRegistryController,
  AdminModelBaseController,
  AdminDataFineTuneController,
  AdminModelFineTuneController,
  AdminSystemKeyLlmController,
} from './controllers';

// Import services
import {
  AdminModelRegistryService,
  AdminModelBaseService,
  AdminDataFineTuneService,
  AdminModelFineTuneService,
  AdminSystemKeyLlmService,
} from './services';

// Import helpers
import { ApiKeyEncryptionHelper } from '../helpers/api-key-encryption.helper';

/**
 * Module quản lý models cho admin
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      entities.ModelRegistry,
      entities.ModelBase,
      entities.SystemKeyLlm,
      entities.ModelBaseKeyLlm,
      entities.FineTuneHistories,
      entities.ModelFineTune,
      entities.UserKeyLlm,
      // entities.DataFineTune,
      // entities.UserDataFineTune,
      // entities.AdminDataFineTune,
    ]),
    ConfigModule,
    HttpModule,
    EmployeeModule,
    ServicesModule,
  ],
  controllers: [
    AdminModelRegistryController,
    AdminModelBaseController,
    AdminDataFineTuneController,
    AdminModelFineTuneController,
    AdminSystemKeyLlmController,
  ],
  providers: [
    // Services
    AdminModelRegistryService,
    AdminModelBaseService,
    AdminDataFineTuneService,
    AdminModelFineTuneService,
    AdminSystemKeyLlmService,

    // Repositories
    repositories.ModelRegistryRepository,
    repositories.ModelBaseRepository,
    repositories.SystemKeyLlmRepository,
    repositories.ModelBaseKeyLlmRepository,
    repositories.FineTuneHistoriesRepository,
    repositories.ModelFineTuneRepository,
    repositories.UserKeyLlmRepository,
    repositories.UserDataFineTuneRepository,
    repositories.AdminDataFineTuneRepository,

    // Helpers
    ApiKeyEncryptionHelper,
  ],
  exports: [
    AdminModelRegistryService,
    AdminModelBaseService,
    AdminDataFineTuneService,
    AdminModelFineTuneService,
    AdminSystemKeyLlmService,
  ],
})
export class ModelsAdminModule {}
