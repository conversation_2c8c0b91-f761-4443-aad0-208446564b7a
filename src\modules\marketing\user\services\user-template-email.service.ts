import { Injectable, ConflictException, BadRequestException } from '@nestjs/common';
import { MoreThanOrEqual } from 'typeorm';
import { UserTemplateEmailRepository } from '../repositories/user-template-email.repository';
import { UserCampaignHistoryRepository } from '../repositories/user-campaign-history.repository';
import { UserTemplateEmail } from '../entities/user-template-email.entity';
import { CreateTemplateEmailDto, TemplateEmailQueryDto, UpdateTemplateEmailDto, TemplateEmailOverviewResponseDto, DeleteMultipleTemplateEmailResultDto } from '../dto/template-email';
import { PaginatedResult } from '@/common/response';
import { TemplateEmailUtils } from '../utils/template-email.utils';

/**
 * Service xử lý logic liên quan đến template email
 */
@Injectable()
export class UserTemplateEmailService {
  constructor(
    private readonly userTemplateEmailRepository: UserTemplateEmailRepository,
    private readonly userCampaignHistoryRepository: UserCampaignHistoryRepository,
  ) {}

  /**
   * Lấy danh sách template email với phân trang và filter
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách template email với phân trang
   */
  async findAll(userId: number, queryDto: TemplateEmailQueryDto): Promise<PaginatedResult<UserTemplateEmail>> {
    return this.userTemplateEmailRepository.findWithPagination(userId, queryDto);
  }

  /**
   * Lấy chi tiết template email
   * @param id ID của template
   * @param userId ID của người dùng
   * @returns Chi tiết template email
   */
  async findById(id: number, userId: number): Promise<UserTemplateEmail> {
    const result = await this.userTemplateEmailRepository.findById(id, userId);
    return this.transformTemplateResponse(result);
  }

  /**
   * Tạo mới template email
   * @param userId ID của người dùng
   * @param createDto Dữ liệu tạo mới
   * @returns Template email đã tạo
   */
  async create(userId: number, createDto: CreateTemplateEmailDto): Promise<UserTemplateEmail> {
    // 1. Validate dữ liệu đầu vào
    await this.validateCreateData(userId, createDto);

    const now = Date.now();

    // 2. Xử lý tags - trim và loại bỏ tags rỗng
    const cleanedTags = createDto.tags ? TemplateEmailUtils.cleanTags(createDto.tags) : [];

    // 3. Xác định content HTML (ưu tiên htmlContent, fallback content)
    const htmlContent = createDto.htmlContent || createDto.content;

    // Validate htmlContent không được undefined
    if (!htmlContent) {
      throw new BadRequestException('htmlContent hoặc content là bắt buộc');
    }

    // 4. Merge placeholders từ HTML content và variables
    const allPlaceholders = TemplateEmailUtils.mergePlaceholders(htmlContent, createDto.variables);

    // 5. Xử lý variable metadata
    const variableMetadata = createDto.variables ?
      TemplateEmailUtils.processVariableMetadata(createDto.variables) : {};

    const templateData: Partial<UserTemplateEmail> = {
      userId,
      name: createDto.name.trim(),
      subject: createDto.subject.trim(),
      htmlContent: htmlContent, // Sử dụng htmlContent đã được xác định
      textContent: createDto.textContent?.trim(),
      type: createDto.type || 'NEWSLETTER',
      previewText: createDto.previewText?.trim(),
      tags: cleanedTags,
      placeholders: allPlaceholders,
      variableMetadata,
      status: 'ACTIVE',
      createdAt: now,
      updatedAt: now,
    };

    const result = await this.userTemplateEmailRepository.create(templateData);
    return this.transformTemplateResponse(result);
  }

  /**
   * Transform template response để thêm backward compatibility
   * @param template Template entity
   * @returns Template với cả htmlContent và content fields
   */
  private transformTemplateResponse(template: UserTemplateEmail): UserTemplateEmail {
    return {
      ...template,
      content: template.htmlContent, // Thêm field content cho backward compatibility
    } as UserTemplateEmail;
  }

  /**
   * Validate dữ liệu tạo template
   * @param userId ID của người dùng
   * @param createDto Dữ liệu tạo mới
   */
  private async validateCreateData(userId: number, createDto: CreateTemplateEmailDto): Promise<void> {
    // 1. Kiểm tra tên template unique
    const existingTemplate = await this.userTemplateEmailRepository.findOne({
      where: { userId, name: createDto.name.trim() }
    });

    if (existingTemplate) {
      throw new ConflictException('Template với tên này đã tồn tại');
    }

    // 2. Validate HTML content cơ bản
    const htmlContent = createDto.htmlContent || createDto.content;
    if (!htmlContent) {
      throw new BadRequestException('htmlContent hoặc content là bắt buộc');
    }
    if (!TemplateEmailUtils.validateHtmlContent(htmlContent)) {
      throw new BadRequestException('Nội dung HTML không hợp lệ');
    }

    // 3. Validate unique variable names
    if (createDto.variables && !TemplateEmailUtils.validateUniqueVariableNames(createDto.variables)) {
      throw new BadRequestException('Tên biến phải unique trong template');
    }
  }

  /**
   * Cập nhật template email
   * @param id ID của template
   * @param userId ID của người dùng
   * @param updateDto Dữ liệu cập nhật
   * @returns Template email đã cập nhật
   */
  async update(id: number, userId: number, updateDto: UpdateTemplateEmailDto): Promise<UserTemplateEmail> {
    const updateData: Partial<UserTemplateEmail> = {
      ...updateDto,
      updatedAt: Date.now(),
    };

    return this.userTemplateEmailRepository.update(id, userId, updateData);
  }

  /**
   * Xóa template email
   * @param id ID của template
   * @param userId ID của người dùng
   * @returns true nếu xóa thành công
   */
  async delete(id: number, userId: number): Promise<boolean> {
    return this.userTemplateEmailRepository.delete(id, userId);
  }

  /**
   * Xóa nhiều template email
   * @param ids Danh sách ID của các template
   * @param userId ID của người dùng
   * @returns Kết quả xóa với thông tin chi tiết
   */
  async deleteMultiple(ids: number[], userId: number): Promise<DeleteMultipleTemplateEmailResultDto> {
    const result = await this.userTemplateEmailRepository.deleteMultiple(ids, userId);

    return {
      deletedIds: result.deletedIds,
      failedIds: result.failedIds,
      totalDeleted: result.deletedIds.length,
      totalFailed: result.failedIds.length,
    };
  }

  /**
   * Lấy thông tin overview về template email
   * @param userId ID của người dùng
   * @returns Thông tin overview template email
   */
  async getOverview(userId: number): Promise<TemplateEmailOverviewResponseDto> {
    const now = Math.floor(Date.now() / 1000);

    // Tính thời gian đầu tuần (thứ 2)
    const currentDate = new Date();
    const dayOfWeek = currentDate.getDay();
    const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Chủ nhật = 0, cần lùi 6 ngày
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - daysToMonday);
    startOfWeek.setHours(0, 0, 0, 0);
    const startOfWeekTimestamp = Math.floor(startOfWeek.getTime() / 1000);

    // 1. Tổng số templates
    const totalTemplates = await this.userTemplateEmailRepository.count({
      where: { userId },
    });

    // 2. Tổng số template hoạt động
    const activeTemplates = await this.userTemplateEmailRepository.count({
      where: { userId, status: 'ACTIVE' },
    });

    // 3. Tổng số template bản nháp
    const draftTemplates = await this.userTemplateEmailRepository.count({
      where: { userId, status: 'DRAFT' },
    });

    // 4. Số template thêm mới tuần này
    const newTemplatesThisWeek = await this.userTemplateEmailRepository.count({
      where: {
        userId,
        createdAt: MoreThanOrEqual(startOfWeekTimestamp),
      },
    });

    // 5. Tổng số đã gửi test tuần này
    // Hiện tại chưa có bảng tracking test email, tạm thời trả về 0
    // TODO: Implement tracking test emails
    const testSentThisWeek = 0;

    return {
      totalTemplates,
      activeTemplates,
      draftTemplates,
      testSentThisWeek,
      newTemplatesThisWeek,
      updatedAt: now,
    };
  }
}
