import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ModelsAdminModule } from './admin/models-admin.module';
import { ModelsUserModule } from './user/models-user.module';
import * as entities from './entities';

/**
 * Module chính cho Model Management System
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      entities.ModelRegistry,
      entities.ModelBase,
      entities.SystemKeyLlm,
      entities.ModelBaseKeyLlm,
      entities.FineTuneHistories,
      entities.ModelFineTune,
      entities.UserKeyLlm,
    ]),
    ModelsAdminModule,
    ModelsUserModule,
  ],
  providers: [],
  exports: [
    ModelsAdminModule,
    ModelsUserModule,
    TypeOrmModule,
  ],
})
export class ModelsModule {}
