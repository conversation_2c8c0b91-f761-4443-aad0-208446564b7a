# Test API Tạo <PERSON> (<PERSON><PERSON><PERSON> nhật)

## Endpoint
```
POST /v1/user/orders
```

## Headers
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

## Request Body Example (Đ<PERSON> cập nhật)

### Tạo đơn hàng với customer ID
```json
{
  "customerInfo": {
    "customerId": 9
  },
  "products": [
    {
      "productId": 1,
      "quantity": 2
    },
    {
      "productId": 2,
      "quantity": 1
    }
  ],
  "billInfo": {
    "subtotal": 300000,
    "tax": 30000,
    "shippingFee": 20000,
    "discount": 10000,
    "total": 340000,
    "paymentMethod": "CASH",
    "paymentStatus": "PENDING",
    "codAmount": 340000
  },
  "hasShipping": true,
  "logisticInfo": {
    "shippingMethod": "Giao hàng nhanh",
    "carrier": "GHN",
    "shippingNote": "Giao hàng trong gi<PERSON> hành <PERSON>"
  },
  "shippingStatus": "PENDING",
  "orderStatus": "PENDING",
  "source": "website",
  "note": "Đơn hàng ưu tiên",
  "tags": ["urgent", "vip"]
}
```

### Tạo đơn hàng tối giản
```json
{
  "customerInfo": {
    "customerId": 9
  },
  "products": [
    {
      "productId": 3,
      "quantity": 1
    }
  ],
  "billInfo": {
    "subtotal": 150000,
    "total": 150000,
    "paymentMethod": "BANKING",
    "paymentStatus": "PAID"
  }
}
```

## Response Example

### Success Response (201 Created)
```json
{
  "success": true,
  "message": "Tạo đơn hàng thành công",
  "data": {
    "id": 123,
    "userConvertCustomerId": 9,
    "userId": 1001,
    "productInfo": {
      "products": [
        {
          "productId": 1,
          "name": "Áo thun nam",
          "quantity": 2,
          "unitPrice": 150000,
          "totalPrice": 300000,
          "description": "Áo thun nam chất liệu cotton",
          "images": ["image1.jpg", "image2.jpg"]
        }
      ]
    },
    "billInfo": {
      "subtotal": 300000,
      "tax": 30000,
      "shippingFee": 20000,
      "discount": 10000,
      "total": 340000,
      "paymentMethod": "CASH",
      "paymentStatus": "PENDING",
      "codAmount": 340000
    },
    "hasShipping": true,
    "shippingStatus": "PENDING",
    "logisticInfo": {
      "shippingMethod": "Giao hàng nhanh",
      "carrier": "GHN",
      "shippingNote": "Giao hàng trong giờ hành chính",
      "deliveryAddress": "123 Đường ABC, Quận 1, TP.HCM",
      "note": "Đơn hàng ưu tiên",
      "tags": ["urgent", "vip"]
    },
    "createdAt": 1741708800000,
    "updatedAt": 1741708800000,
    "source": "website",
    "orderStatus": "PENDING"
  }
}
```

## Thay đổi chính

### ✅ **Đã cập nhật:**

1. **CustomerInfo đơn giản hóa:**
   - Chỉ cần truyền `customerId`
   - Hệ thống tự động lấy thông tin khách hàng từ database
   - Validate customer thuộc về user hiện tại

2. **Địa chỉ giao hàng tự động:**
   - Sử dụng địa chỉ từ customer database
   - Có thể override bằng `deliveryAddress` trong `logisticInfo`

3. **Validation chặt chẽ:**
   - Customer phải tồn tại và thuộc về user
   - Products phải thuộc về user
   - Tự động tính giá từ database

### 📋 **Request Structure:**

```typescript
interface CreateOrderRequest {
  customerInfo: {
    customerId: number;  // Bắt buộc
  };
  products: Array<{
    productId: number;   // Bắt buộc
    quantity: number;    // Bắt buộc
  }>;
  billInfo: {
    subtotal: number;    // Bắt buộc
    tax?: number;        // Tùy chọn
    shippingFee?: number; // Tùy chọn
    discount?: number;   // Tùy chọn
    total: number;       // Bắt buộc
    paymentMethod: 'CASH' | 'CREDIT_CARD' | 'BANKING' | 'E_WALLET'; // Bắt buộc
    paymentStatus?: 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED' | 'PARTIAL_PAID' | 'CANCELLED';
    codAmount?: number;  // Tùy chọn
  };
  hasShipping?: boolean; // Mặc định: true
  logisticInfo?: {
    shippingMethod?: string;
    carrier?: string;
    shippingNote?: string;
    deliveryAddress?: string; // Override địa chỉ customer
  };
  shippingStatus?: string; // Mặc định: 'PENDING'
  orderStatus?: string;    // Mặc định: 'PENDING'
  source?: string;         // Mặc định: 'website'
  note?: string;
  tags?: string[];
}
```

## Error Responses

### 404 Not Found - Customer Not Found
```json
{
  "success": false,
  "message": "Khách hàng không tồn tại hoặc không thuộc về bạn",
  "error": {
    "code": 30181,
    "message": "Khách hàng không tồn tại"
  }
}
```

### 400 Bad Request - Invalid Customer ID
```json
{
  "success": false,
  "message": "Dữ liệu đầu vào không hợp lệ",
  "error": {
    "code": 400,
    "details": [
      {
        "field": "customerInfo.customerId",
        "message": "ID khách hàng không được để trống"
      }
    ]
  }
}
```

## Enums (Không thay đổi)

### PaymentMethodEnum
- `CASH` - Tiền mặt
- `CREDIT_CARD` - Thẻ tín dụng
- `BANKING` - Chuyển khoản
- `E_WALLET` - Ví điện tử

### PaymentStatusEnum
- `PENDING` - Chờ thanh toán
- `PAID` - Đã thanh toán
- `FAILED` - Thanh toán thất bại
- `REFUNDED` - Đã hoàn tiền
- `PARTIAL_PAID` - Thanh toán một phần
- `CANCELLED` - Đã hủy

## Notes

1. **Customer ID bắt buộc**: Phải truyền `customerId` hợp lệ
2. **Tự động lấy thông tin**: Hệ thống tự động lấy tên, phone, email, địa chỉ từ database
3. **Địa chỉ giao hàng**: Mặc định sử dụng địa chỉ customer, có thể override
4. **Validation**: Customer và products phải thuộc về user hiện tại
5. **Giá tự động**: Hệ thống tự động tính giá từ thông tin sản phẩm
6. **Đơn giản hóa**: Giảm thiểu input cần thiết, tăng tính chính xác
