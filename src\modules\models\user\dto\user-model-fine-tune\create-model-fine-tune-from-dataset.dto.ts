import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsUUID, MaxLength } from 'class-validator';

/**
 * DTO cho việc tạo model fine tune từ dataset và model base
 */
export class CreateModelFineTuneFromDatasetDto {
  /**
   * Tên model fine-tune
   */
  @ApiProperty({
    description: 'Tên model fine-tune',
    example: 'My Custom GPT Model',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * Tên base model được fine-tune
   */
  @ApiProperty({
    description: 'Tên base model được fine-tune',
    example: 'gpt-3.5-turbo',
  })
  @IsString()
  @IsNotEmpty()
  baseModelName: string;

  /**
   * ID của dataset để fine-tune
   */
  @ApiProperty({
    description: 'UUID của dataset để fine-tune',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  datasetId: string;

  /**
   * <PERSON><PERSON>n kết đến model_registry
   */
  @ApiPropertyOptional({
    description: 'UUID của model registry capabilities',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  capabilities?: string;

  /**
   * System key dùng để huấn luyện
   */
  @ApiPropertyOptional({
    description: 'UUID của system key LLM để huấn luyện',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  keyLlmId?: string;
}
