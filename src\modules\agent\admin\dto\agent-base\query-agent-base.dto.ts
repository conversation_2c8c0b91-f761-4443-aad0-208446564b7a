import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, IsUUID } from 'class-validator';
import { Transform } from 'class-transformer';
import { QueryDto } from '@common/dto';

/**
 * DTO cho việc truy vấn danh sách agent base
 */
export class QueryAgentBaseDto extends QueryDto {
  /**
   * Lọc theo trạng thái hoạt động
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái hoạt động',
    example: true,
    type: Boolean,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: 'Active phải là boolean' })
  active?: boolean;

  /**
   * Lọc theo ID của model registry
   */
  @ApiProperty({
    description: '<PERSON>ọc theo ID của model registry',
    example: '123e4567-e89b-12d3-a456-************',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsUUID(4, { message: 'Model registry ID phải là UUID hợp lệ' })
  modelRegistryId?: string;

  /**
   * Lọc theo tên model
   */
  @ApiProperty({
    description: 'Lọc theo tên model',
    example: 'GPT-4',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Model name phải là string' })
  modelName?: string;

  /**
   * Lọc theo ID của key LLM
   */
  @ApiProperty({
    description: 'Lọc theo ID của key LLM',
    example: '123e4567-e89b-12d3-a456-************',
    type: String,
    required: false,
  })
  @IsOptional()
  @IsUUID(4, { message: 'Key LLM ID phải là UUID hợp lệ' })
  keyLlmId?: string;

  /**
   * Bao gồm cả các bản ghi đã bị soft delete
   */
  @ApiProperty({
    description: 'Bao gồm cả các bản ghi đã bị soft delete',
    example: false,
    type: Boolean,
    required: false,
    default: false,
  })
  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  @IsBoolean({ message: 'Include deleted phải là boolean' })
  includeDeleted?: boolean = false;
}
