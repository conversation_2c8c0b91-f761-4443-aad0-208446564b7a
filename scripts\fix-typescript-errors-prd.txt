# Product Requirements Document - Fix TypeScript Errors in RedAI V2 Backend

## Mô tả dự án
Sửa các lỗi TypeScript trong ứng dụng RedAI V2 Backend khi chạy `npm run start:dev`. C<PERSON> 146 lỗi cần đ<PERSON><PERSON><PERSON>, chủ yếu tập trung vào 2 modules:

### 1. Agent Module - Model Integration Issues
- **Vấn đề chính**: Luồng hoạt động model không đúng với thiết kế mới
- **Agent-Base, Agent-System, Agent-Strategy**: Nhận vào `model_name` và `llm_key_id`, cần ánh xạ qua `model-registry.entity.ts` để lấy `model_registry_id` trước khi lưu DB
- **Agent-Template**: Sử dụng `model_id` từ model-base
- **Agent-User**: 2 trường hợp:
  - Dùng model hệ thống: chỉ cần `model_base_id`
  - Dùng model ngoà<PERSON> hệ thống: cần 3 trường `model_name`, `model_registry_id`, `llm_key_id`

### 2. Models Module - Query and Import Issues
- **Vấn đề**: Lỗi truy vấn, biến và import không đúng
- **Repository issues**: Missing methods, wrong return types
- **Mapper issues**: Type mismatches, null vs undefined
- **Service issues**: Missing imports, wrong query parameters

## Yêu cầu chức năng

### Agent Module Fixes
1. **Model Resolution Logic**
   - Implement model name to registry mapping
   - Fix model_base_id, model_finetuning_id handling
   - Update entity properties and DTOs

2. **Entity Updates**
   - Add missing properties to Agent, AgentBase, AgentSystem, AgentUser entities
   - Fix TypeAgentConfig properties
   - Update status handling

3. **Service Layer Fixes**
   - Fix import paths for model-training modules
   - Implement proper model validation
   - Update create/update logic for agents

### Models Module Fixes
1. **Repository Fixes**
   - Fix restore method signatures
   - Add missing query methods
   - Fix null/undefined type issues

2. **Mapper Fixes**
   - Handle null vs undefined conversions
   - Fix property mappings
   - Update response DTOs

3. **Service Fixes**
   - Fix import paths
   - Update query parameter handling
   - Fix API response handling

## Yêu cầu kỹ thuật

### Constraints
- Maintain backward compatibility
- Follow existing code patterns
- Use proper TypeScript typing
- No breaking changes to APIs

### Dependencies
- TypeORM entities and repositories
- NestJS services and controllers
- Existing model-training module structure

## Acceptance Criteria
1. All 146 TypeScript errors resolved
2. `npm run start:dev` runs successfully
3. No breaking changes to existing APIs
4. Proper model resolution logic implemented
5. All imports and dependencies working correctly

## Priority
**HIGH** - Blocking development and deployment

## Estimated Complexity
**HIGH** - Multiple modules affected, complex model relationships
