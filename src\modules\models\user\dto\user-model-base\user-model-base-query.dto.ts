import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsBoolean } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';
import { ProviderEnumq } from '@/shared/services/ai/utils/type-provider.util';

/**
 * Enum cho các trường có thể sắp xếp
 */
export enum UserModelBaseSortBy {
  NAME = 'name',
  PROVIDER = 'provider',
  STATUS = 'status',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  INPUT_COST = 'inputCostPer1kTokens',
  OUTPUT_COST = 'outputCostPer1kTokens',
  CONTEXT_LENGTH = 'contextLength',
}

/**
 * DTO cho việc truy vấn danh sách user model base
 */
export class UserModelBaseQueryDto extends QueryDto {
  /**
   * Tìm kiếm theo tên model
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên model',
    example: 'gpt-4',
  })
  @IsOptional()
  @IsString()
  name?: string;

  /**
   * Tìm kiếm theo model ID
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo model ID',
    example: 'gpt-4-turbo',
  })
  @IsOptional()
  @IsString()
  modelId?: string;

  /**
   * Lọc theo nhà cung cấp
   */
  @ApiPropertyOptional({
    description: 'Lọc theo nhà cung cấp',
    enum: ProviderEnumq,
    example: ProviderEnumq.OPENAI,
  })
  @IsOptional()
  @IsEnum(ProviderEnumq)
  provider?: ProviderEnumq;

  /**
   * Lọc theo khả năng user access
   */
  @ApiPropertyOptional({
    description: 'Lọc theo khả năng user access',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isUserAccessible?: boolean;

  /**
   * Lọc theo khả năng fine-tune
   */
  @ApiPropertyOptional({
    description: 'Lọc theo khả năng fine-tune',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isFineTunable?: boolean;

  /**
   * Lọc theo source (admin hoặc user-key)
   */
  @ApiPropertyOptional({
    description: 'Lọc theo source (admin hoặc user-key)',
    enum: ['admin', 'user-key'],
    example: 'admin',
  })
  @IsOptional()
  @IsString()
  source?: 'admin' | 'user-key';

  /**
   * ID của user key (nếu lọc theo user-key)
   */
  @ApiPropertyOptional({
    description: 'ID của user key (nếu lọc theo user-key)',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsOptional()
  @IsString()
  keyId?: string;

  /**
   * Lọc theo capabilities
   */
  @ApiPropertyOptional({
    description: 'Lọc theo capabilities (text-generation, image-generation, etc.)',
    example: 'text-generation',
  })
  @IsOptional()
  @IsString()
  capability?: string;

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: UserModelBaseSortBy,
    example: UserModelBaseSortBy.NAME,
  })
  @IsOptional()
  @IsEnum(UserModelBaseSortBy)
  sortBy?: UserModelBaseSortBy = UserModelBaseSortBy.NAME;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.ASC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.ASC;
}
