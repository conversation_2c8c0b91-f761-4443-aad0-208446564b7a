import { Injectable, Logger } from '@nestjs/common';
import { TypeAgentRepository } from '@modules/agent/repositories/type-agent.repository';
import {
  CreateTypeAgentDto,
  TypeAgentDetailDto,
  TypeAgentListItemDto,
  TypeAgentQueryDto,
  TypeAgentTrashItemDto,
  UpdateTypeAgentDto,
  UpdateTypeAgentStatusDto,
} from '../dto/type-agent';
import { TypeAgent } from '@modules/agent/entities';
import { TypeAgentStatus } from '@modules/agent/constants';
import { PaginatedResult } from '@/common/response';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { Transactional } from 'typeorm-transactional';

import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { EmployeeInfoDto } from '@modules/agent/admin/dto/common';
import { EmployeeInfoSimpleDto } from '@/modules/employee/dto/employee-info-simple.dto';

/**
 * Service xử lý logic nghiệp vụ cho Type Agent
 */
@Injectable()
export class AdminTypeAgentService {
  private readonly logger = new Logger(AdminTypeAgentService.name);

  constructor(
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly employeeInfoService: EmployeeInfoService,
  ) {}

  /**
   * Lấy danh sách loại agent với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách loại agent với phân trang
   */
  async findAll(
    queryDto: TypeAgentQueryDto,
  ): Promise<PaginatedResult<TypeAgentListItemDto>> {
    try {
      const { page, limit, search, sortBy, sortDirection } = queryDto;

      // Lấy danh sách loại agent từ repository (không filter theo status)
      const result = await this.typeAgentRepository.findPaginated(
        page,
        limit,
        search,
        undefined, // Không filter theo status
        undefined,
        sortBy,
        sortDirection,
      );

      // Chuyển đổi kết quả sang DTO
      const items = await Promise.all(
        result.items.map((typeAgent) => this.mapToListItemDto(typeAgent))
      );

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Error finding type agents: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED);
    }
  }

  /**
   * Lấy thông tin chi tiết loại agent theo ID
   * @param id ID của loại agent
   * @returns Thông tin chi tiết loại agent
   */
  async findById(id: number): Promise<TypeAgentDetailDto> {
    // Lấy thông tin loại agent từ repository
    const typeAgent = await this.typeAgentRepository.findById(id);

    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    try {
      return await this.mapToDetailDto(typeAgent);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error finding type agent by id: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED);
    }
  }

  /**
   * Tạo loại agent mới
   * @param createDto Dữ liệu tạo loại agent
   * @param employeeId ID của nhân viên tạo
   * @returns ID của loại agent đã tạo
   */
  @Transactional()
  async create(
    createDto: CreateTypeAgentDto,
    employeeId: number,
  ): Promise<number> {
    // Kiểm tra tên loại agent đã tồn tại chưa
    const existingTypeAgent = await this.typeAgentRepository.findByName(
      createDto.name,
    );
    if (existingTypeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS);
    }

    try {
      // Kiểm tra các admin tools có tồn tại không
      await this.validateAdminTools(createDto.toolIds);

      // Tạo loại agent mới
      const typeAgent = new TypeAgent();
      typeAgent.name = createDto.name;
      typeAgent.description = createDto.description;
      typeAgent.config = createDto.defaultConfig;
      typeAgent.status = createDto.status;
      typeAgent.createdBy = employeeId;
      typeAgent.updatedBy = employeeId;

      // Lưu loại agent
      const savedTypeAgent = await this.typeAgentRepository.save(typeAgent);

      // Liên kết với các admin tools
      await this.linkAdminTools(savedTypeAgent.id, createDto.toolIds);

      return savedTypeAgent.id;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error creating type agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_CREATION_FAILED);
    }
  }

  /**
   * Cập nhật thông tin loại agent
   * @param id ID của loại agent
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   */
  @Transactional()
  async update(
    id: number,
    updateDto: UpdateTypeAgentDto,
    employeeId: number,
  ): Promise<void> {
    // Kiểm tra loại agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findById(id);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Kiểm tra xem loại agent đã bị xóa mềm hay chưa
    if (typeAgent.deletedAt) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_ALREADY_DELETED);
    }

    // Kiểm tra tên loại agent đã tồn tại chưa (nếu có cập nhật tên)
    if (updateDto.name && updateDto.name !== typeAgent.name) {
      const existingTypeAgent = await this.typeAgentRepository.findByName(
        updateDto.name,
      );
      if (existingTypeAgent && existingTypeAgent.id !== id) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS);
      }
    }
    try {
      // Kiểm tra các admin tools có tồn tại không (nếu có cập nhật tools)
      if (updateDto.toolIds) {
        await this.validateAdminTools(updateDto.toolIds);
      }

      // Cập nhật thông tin loại agent
      if (updateDto.name) typeAgent.name = updateDto.name;
      if (updateDto.description !== undefined)
        typeAgent.description = updateDto.description;
      if (updateDto.defaultConfig) typeAgent.config = updateDto.defaultConfig;
      if (updateDto.status) typeAgent.status = updateDto.status;
      typeAgent.updatedBy = employeeId;

      // Lưu loại agent
      await this.typeAgentRepository.save(typeAgent);

      // Cập nhật liên kết với các admin tools (nếu có)
      if (updateDto.toolIds) {
        await this.linkAdminTools(id, updateDto.toolIds);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating type agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Cập nhật trạng thái loại agent
   * @param id ID của loại agent
   * @param updateStatusDto Dữ liệu cập nhật trạng thái
   * @param employeeId ID của nhân viên cập nhật
   */
  @Transactional()
  async updateStatus(
    id: number,
    updateStatusDto: UpdateTypeAgentStatusDto,
    employeeId: number,
  ): Promise<void> {
    // Kiểm tra loại agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findById(id);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Kiểm tra xem loại agent đã bị xóa mềm hay chưa
    if (typeAgent.deletedAt) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_ALREADY_DELETED);
    }
    try {
      // Cập nhật trạng thái
      typeAgent.status = updateStatusDto.status;
      typeAgent.updatedBy = employeeId;

      // Lưu loại agent
      await this.typeAgentRepository.save(typeAgent);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating type agent status: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_STATUS_UPDATE_FAILED);
    }
  }

  /**
   * Xóa loại agent (soft delete)
   * @param id ID của loại agent
   * @param employeeId ID của nhân viên xóa
   */
  @Transactional()
  async remove(id: number, employeeId: number): Promise<void> {
    // Kiểm tra loại agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findById(id);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }
    try {
      // Sử dụng phương thức xóa mềm tùy chỉnh thay vì softDelete của TypeORM
      const result = await this.typeAgentRepository.customSoftDelete(
        id,
        employeeId,
      );

      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
      }

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error removing type agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
    }
  }



  /**
   * Kiểm tra các admin tools có tồn tại không
   * @param toolIds Danh sách ID của các admin tools
   */
  private async validateAdminTools(toolIds: string[]): Promise<void> {
    const validation = await this.typeAgentRepository.validateAdminTools(toolIds);

    if (!validation.valid) {
      throw new AppException(AGENT_ERROR_CODES.TOOL_NOT_FOUND);
    }
  }

  /**
   * Liên kết type agent với admin tools
   * @param typeAgentId ID của type agent
   * @param toolIds Danh sách ID của admin tools
   */
  private async linkAdminTools(typeAgentId: number, toolIds: string[]): Promise<void> {
    await this.typeAgentRepository.linkAdminTools(typeAgentId, toolIds);
  }

  /**
   * Lấy danh sách tools cho type agent
   * @param typeAgentId ID của type agent
   * @returns Danh sách tools
   */
  private async getToolsForTypeAgent(typeAgentId: number) {
    return await this.typeAgentRepository.getAdminToolsByTypeAgentId(typeAgentId);
  }

  /**
   * Cập nhật trạng thái type agent theo query param
   * @param id ID của type agent
   * @param draft true để chuyển về DRAFT, false để chuyển về APPROVED
   * @param employeeId ID của nhân viên cập nhật
   */
  async updateStatusByQuery(id: number, draft: boolean, employeeId: number): Promise<void> {
    try {
      // Kiểm tra type agent có tồn tại không
      const typeAgent = await this.typeAgentRepository.findById(id);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Xác định status mới
      const newStatus = draft ? TypeAgentStatus.DRAFT : TypeAgentStatus.APPROVED;

      // Cập nhật status
      typeAgent.status = newStatus;
      typeAgent.updatedBy = employeeId;

      await this.typeAgentRepository.save(typeAgent);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating type agent status: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_STATUS_UPDATE_FAILED);
    }
  }

  /**
   * Xóa type agent với migration agents sang type mới
   * @param id ID của type agent cần xóa
   * @param newTypeAgentId ID của type agent mới để migrate
   * @param employeeId ID của nhân viên xóa
   * @returns Số lượng agents đã được migrate
   */
  async removeWithMigration(id: number, newTypeAgentId: number, employeeId: number): Promise<number> {
    try {
      // Kiểm tra type agent cần xóa có tồn tại không
      const typeAgent = await this.typeAgentRepository.findById(id);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Kiểm tra type agent mới có tồn tại không
      const newTypeAgent = await this.typeAgentRepository.findById(newTypeAgentId);
      if (!newTypeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Migrate agents sang type mới
      const migratedCount = await this.typeAgentRepository.migrateAgents(id, newTypeAgentId);

      // Soft delete type agent
      await this.typeAgentRepository.customSoftDelete(id, employeeId);

      return migratedCount;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing type agent with migration: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
    }
  }

  /**
   * Lấy danh sách type agents đã xóa mềm
   * @param queryDto Tham số truy vấn
   * @returns Danh sách type agents đã xóa với phân trang
   */
  async findAllDeleted(queryDto: TypeAgentQueryDto): Promise<PaginatedResult<TypeAgentTrashItemDto>> {
    try {
      const result = await this.typeAgentRepository.findDeletedPaginated(
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.sortBy,
        queryDto.sortDirection,
      );

      // Chuyển đổi kết quả sang DTO
      const items = await Promise.all(
        result.items.map((typeAgent) => this.mapToTrashItemDto(typeAgent))
      );

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Error fetching deleted type agents: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED);
    }
  }

  /**
   * Khôi phục type agent đã xóa mềm
   * @param id ID của type agent cần khôi phục
   */
  async restore(id: number): Promise<void> {
    try {
      const success = await this.typeAgentRepository.restoreTypeAgent(id);
      if (!success) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error restoring type agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
    }
  }



  /**
   * Khôi phục nhiều type agents đã xóa mềm
   * @param ids Danh sách ID của các type agents cần khôi phục
   * @returns Số lượng type agents đã khôi phục thành công
   */
  async bulkRestore(ids: number[]): Promise<number> {
    try {
      let restoredCount = 0;

      // Xử lý từng type agent
      for (const id of ids) {
        try {
          const success = await this.typeAgentRepository.restoreTypeAgent(id);
          if (success) {
            restoredCount++;
          } else {
            this.logger.warn(`Type agent ${id} không thể khôi phục (có thể không tồn tại hoặc chưa bị xóa)`);
          }
        } catch (error) {
          this.logger.error(`Lỗi khi khôi phục type agent ${id}: ${error.message}`);
          // Tiếp tục với các ID khác
        }
      }

      return restoredCount;
    } catch (error) {
      this.logger.error(`Error bulk restoring type agents: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
    }
  }

  /**
   * Chuyển đổi TypeAgent thành TypeAgentListItemDto
   * @param typeAgent Entity TypeAgent
   * @returns TypeAgentListItemDto
   */
  private async mapToListItemDto(
    typeAgent: TypeAgent,
  ): Promise<TypeAgentListItemDto> {
    const countTool = await this.typeAgentRepository.countAdminToolsByTypeAgentId(typeAgent.id);

    return {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description,
      createdAt: typeAgent.createdAt,
      status: typeAgent.status,
      countTool,
    };
  }

  /**
   * Chuyển đổi TypeAgent thành TypeAgentDetailDto
   * @param typeAgent Entity TypeAgent
   * @returns TypeAgentDetailDto
   */
  private async mapToDetailDto(
    typeAgent: TypeAgent,
  ): Promise<TypeAgentDetailDto> {

    return {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description,
      defaultConfig: typeAgent.config,
      status: typeAgent.status,
      createdAt: typeAgent.createdAt,
      updatedAt: typeAgent.updatedAt,
      tools: await this.getToolsForTypeAgent(typeAgent.id),
    };
  }

  /**
   * Chuyển đổi TypeAgent thành TypeAgentTrashItemDto
   * @param typeAgent Entity TypeAgent đã bị xóa
   * @returns TypeAgentTrashItemDto
   */
  private async mapToTrashItemDto(typeAgent: TypeAgent): Promise<TypeAgentTrashItemDto> {
    const countTool = await this.typeAgentRepository.countAdminToolsByTypeAgentId(typeAgent.id);

    return {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description,
      status: typeAgent.status,
      createdAt: typeAgent.createdAt,
      deletedAt: typeAgent.deletedAt || 0,
      countTool,
    };
  }

  /**
   * Lấy thông tin nhân viên
   * @param employeeId ID của nhân viên
   * @returns Thông tin nhân viên
   */
  private async getEmployeeInfo(employeeId: number): Promise<EmployeeInfoSimpleDto> {
    return await this.employeeInfoService.getEmployeeInfo(employeeId);
  }
}
