import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { SortDirection } from '@common/dto/query.dto';
import { OrderStatusEnum, ShippingStatusEnum } from '../../enums';

/**
 * Enum cho các trường sắp xếp đơn hàng
 */
export enum UserOrderSortField {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  SHIPPING_STATUS = 'shippingStatus',
  ORDER_STATUS = 'orderStatus',
}

/**
 * DTO cho các tham số truy vấn danh sách đơn hàng
 */
export class QueryUserOrderDto {
  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number = 1;

  @ApiProperty({
    description: 'Số lượng bản ghi trên một trang',
    example: 10,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number = 10;

  @ApiProperty({
    description: 'ID của khách hàng',
    example: 101,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  userConvertCustomerId?: number;

  @ApiProperty({
    description: 'Trạng thái vận chuyển',
    enum: ShippingStatusEnum,
    example: ShippingStatusEnum.PENDING,
    required: false,
  })
  @IsOptional()
  @IsEnum(ShippingStatusEnum)
  shippingStatus?: ShippingStatusEnum;

  @ApiProperty({
    description: 'Nguồn đơn hàng',
    example: 'website',
    required: false,
  })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiProperty({
    description: 'Trạng thái đơn hàng',
    enum: OrderStatusEnum,
    example: OrderStatusEnum.PENDING,
    required: false,
  })
  @IsOptional()
  @IsEnum(OrderStatusEnum)
  orderStatus?: OrderStatusEnum;

  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: UserOrderSortField,
    default: UserOrderSortField.CREATED_AT,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserOrderSortField)
  sortBy?: UserOrderSortField = UserOrderSortField.CREATED_AT;

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
