import { <PERSON><PERSON><PERSON>, <PERSON>C<PERSON>umn, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { TypeAgent } from './type-agent.entity';
import { AdminTool } from '@modules/tools/entities';

/**
 * Entity đại diện cho bảng admin_type_agent_tools trong cơ sở dữ liệu
 * Bảng liên kết ánh xạ admin tools với type agent
 * Chỉ admin mới có thể tạo type-agent và liên kết với admin tools
 */
@Entity('admin_type_agent_tools')
export class AdminTypeAgentTools {
  /**
   * ID của admin tool (UUID)
   * Là một phần của khóa chính
   */
  @PrimaryColumn({ name: 'tool_id', type: 'uuid' })
  toolId: string;

  /**
   * ID của type agent
   * Là một phần của khóa chính
   */
  @PrimaryColumn({ name: 'type_id', type: 'integer' })
  typeId: number;
}
