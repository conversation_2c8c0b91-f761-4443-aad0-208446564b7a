import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ProviderEnum } from '../constants/provider.enum';

/**
 * Entity đại diện cho bảng system_key_llm trong cơ sở dữ liệu
 * Lưu trữ API key LLM dùng ở cấp hệ thống (dùng chung)
 */
@Entity('system_key_llm')
export class SystemKeyLlm {
  /**
   * UUID của key
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên định danh cho key
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * Nhà cung cấp model (ví dụ: OPENAI)
   */
  @Column({
    name: 'provider',
    type: 'enum',
    enum: ProviderEnum,
    default: ProviderEnum.OPENAI,
  })
  provider: ProviderEnum;

  /**
   * API key đã được mã hóa
   */
  @Column({
    type: 'text',
    name: 'api_key',
    comment: 'API key đã được mã hóa'
  })
  apiKey: string;

  /**
   * Thời gian tạo key
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Người tạo key
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number;

  /**
   * Thời gian cập nhật
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Người cập nhật
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * Thời điểm xóa key
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;

  /**
   * Người thực hiện xóa
   */
  @Column({ name: 'deleted_by', type: 'bigint', nullable: true })
  deletedBy: number | null;
}
