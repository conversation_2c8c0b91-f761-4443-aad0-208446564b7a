import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { ServicesModule } from '@shared/services/services.module';

// Import entities
import * as entities from '../entities';

// Import repositories
import * as repositories from '../repositories';

// Import controllers
import {
  UserModelBaseController,
  UserDataFineTuneController,
  UserModelFineTuneController,
  UserKeyLlmController,
} from './controllers';

// Import services
import {
  UserModelBaseService,
  UserDataFineTuneService,
  UserModelFineTuneService,
  UserKeyLlmService,
} from './services';

// Import helpers
import { ApiKeyEncryptionHelper } from '../helpers/api-key-encryption.helper';

/**
 * Module quản lý models cho user
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      entities.ModelRegistry,
      entities.ModelBase,
      entities.SystemKeyLlm,
      entities.ModelBaseKeyLlm,
      entities.FineTuneHistories,
      entities.ModelFineTune,
      entities.UserKeyLlm,
    ]),
    ConfigModule,
    ServicesModule,
  ],
  controllers: [
    UserModelBaseController,
    UserDataFineTuneController,
    UserModelFineTuneController,
    UserKeyLlmController,
  ],
  providers: [
    // Services
    UserModelBaseService,
    UserDataFineTuneService,
    UserModelFineTuneService,
    UserKeyLlmService,

    // Repositories
    repositories.ModelRegistryRepository,
    repositories.ModelBaseRepository,
    repositories.SystemKeyLlmRepository,
    repositories.ModelBaseKeyLlmRepository,
    repositories.FineTuneHistoriesRepository,
    repositories.ModelFineTuneRepository,
    repositories.UserKeyLlmRepository,
    repositories.UserDataFineTuneRepository,
    repositories.AdminDataFineTuneRepository,

    // Helpers
    ApiKeyEncryptionHelper,
  ],
  exports: [
    UserModelBaseService,
    UserDataFineTuneService,
    UserModelFineTuneService,
    UserKeyLlmService,
  ],
})
export class ModelsUserModule {}
