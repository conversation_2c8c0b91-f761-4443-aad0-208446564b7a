import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentEmployee } from '@/modules/auth/decorators';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import {
  CreateSystemKeyLlmDto,
  SystemKeyLlmDetailResponseDto,
  SystemKeyLlmQueryDto,
  SystemKeyLlmResponseDto,
  UpdateSystemKeyLlmDto
} from '../dto/system-key-llm';
import { AdminSystemKeyLlmService } from '../services';

/**
 * Controller xử lý API cho Admin System Key LLM
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_API_KEY_MODEL)
@Controller('admin/system-key-llm')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class AdminSystemKeyLlmController {
  constructor(private readonly adminSystemKeyLlmService: AdminSystemKeyLlmService) { }

  /**
   * Tạo mới system key LLM
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo mới system key LLM',
    description: 'Chỉ cần 3 trường bắt buộc: name, provider, apiKey. Các trường khác sẽ có giá trị mặc định.'
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới system key LLM thành công',
    type: ApiResponseDto
  })
  create(
    @Body() createDto: CreateSystemKeyLlmDto,
    @CurrentEmployee('id') employeeId: number
  ): Promise<ApiResponseDto<{ id: string }>> {
    return this.adminSystemKeyLlmService.create(createDto, employeeId);
  }

  /**
   * Lấy danh sách system key LLM có phân trang và tìm kiếm
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách system key LLM có phân trang và tìm kiếm',
    description: 'API này hỗ trợ tìm kiếm theo tên key, phân trang và sắp xếp'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách system key LLM',
    type: ApiResponseDto
  })
  findAll(@Query() queryDto: SystemKeyLlmQueryDto): Promise<ApiResponseDto<PaginatedResult<SystemKeyLlmResponseDto>>> {
    return this.adminSystemKeyLlmService.findAll(queryDto);
  }

  /**
   * Lấy chi tiết system key LLM
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết system key LLM' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết system key LLM',
    type: ApiResponseDto
  })
  findOne(@Param('id') id: string): Promise<ApiResponseDto<SystemKeyLlmDetailResponseDto>> {
    return this.adminSystemKeyLlmService.findOne(id);
  }

  /**
   * Cập nhật system key LLM
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật system key LLM' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật system key LLM thành công',
    type: ApiResponseDto
  })
  async update(
    @Param('id') id: string,
    @Body() updateDto: UpdateSystemKeyLlmDto,
    @CurrentEmployee('id') employeeId: number
  ): Promise<ApiResponseDto<{ id: string }>> {
    const result = await this.adminSystemKeyLlmService.update(id, updateDto, employeeId);
    return ApiResponseDto.success(result);
  }

  /**
   * Xóa system key LLM (soft delete)
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa system key LLM' })
  @ApiResponse({
    status: 200,
    description: 'Xóa system key LLM thành công',
    type: ApiResponseDto
  })
  remove(
    @Param('id') id: string,
    @CurrentEmployee('id') employeeId: number
  ): Promise<ApiResponseDto<{ id: string }>> {
    return this.adminSystemKeyLlmService.remove(id, employeeId);
  }

  /**
   * Lấy danh sách system key LLM đã xóa
   */
  @Get('deleted/list')
  @ApiOperation({ summary: 'Lấy danh sách system key LLM đã xóa' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách system key LLM đã xóa',
    type: ApiResponseDto
  })
  findDeleted(@Query() queryDto: SystemKeyLlmQueryDto) {
    return this.adminSystemKeyLlmService.findDeleted(queryDto);
  }

  /**
   * Khôi phục system key LLM đã xóa
   */
  @Patch(':id/restore')
  @ApiOperation({ summary: 'Khôi phục system key LLM đã xóa' })
  @ApiResponse({
    status: 200,
    description: 'Khôi phục system key LLM thành công',
    type: ApiResponseDto
  })
  restore(
    @Body() ids: string[],
    @CurrentEmployee('id') employeeId: number
  ) {
    return this.adminSystemKeyLlmService.restore(ids, employeeId);
  }

  /**
   * Test kết nối API key
   */
  @Post(':id/test-connection')
  @ApiOperation({ summary: 'Test kết nối API key' })
  @ApiResponse({
    status: 200,
    description: 'Kết quả test kết nối',
    type: ApiResponseDto
  })
  testConnection(@Param('id') id: string) {
    return this.adminSystemKeyLlmService.testConnection(id);
  }
}
