import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Agent, AgentBase } from '@modules/agent/entities';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';
import { ModelConfigResponseDto, VectorStoreDto, EmployeeInfoDto } from '@modules/agent/admin/dto/common';
import { AvatarUrlHelper } from '@modules/agent/admin/helpers/avatar-url.helper';
import { CdnService } from '@shared/services/cdn.service';
import { MultiAgentRelationResponseDto } from './multi-agent-relation-response.dto';

/**
 * DTO cho thông tin model được sử dụng bởi agent
 */
export class ModelInfoDto {
  /**
   * ID của base model (nếu có)
   */
  @ApiPropertyOptional({
    description: 'ID của base model (nếu có)',
    example: 'base-model-uuid',
  })
  model_base_id?: string | null;

  /**
   * ID của finetuning model (nếu có)
   */
  @ApiPropertyOptional({
    description: 'ID của finetuning model (nếu có)',
    example: 'finetuning-model-uuid',
  })
  model_finetuning_id?: string | null;

  /**
   * Model ID từ provider
   */
  @ApiPropertyOptional({
    description: 'Model ID từ provider',
    example: 'gpt-4o',
  })
  model_id?: string | null;

  /**
   * Loại provider
   */
  @ApiPropertyOptional({
    description: 'Loại provider',
    example: 'OPENAI',
  })
  typeProvider?: string | null;
}

/**
 * DTO cho việc trả về thông tin agent base trong danh sách
 */
export class AgentBaseListItemDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  name: string;

  /**
   * URL avatar của agent
   */
  @ApiPropertyOptional({
    description: 'URL avatar của agent',
    example: 'https://example.com/system-assistant.png',
  })
  avatar: string | null;

  /**
   * Tên model sử dụng
   */
  @ApiProperty({
    description: 'Tên model sử dụng',
    example: 'gpt-4o',
  })
  model: string;

  /**
   * Model ID từ provider
   */
  @ApiPropertyOptional({
    description: 'Model ID từ provider',
    example: 'gpt-4o',
  })
  model_id?: string | null;

  /**
   * Loại provider
   */
  @ApiPropertyOptional({
    description: 'Loại provider',
    example: 'OPENAI',
  })
  type_provider?: string | null;

  /**
   * Trạng thái của agent
   */
  @ApiProperty({
    description: 'Trạng thái của agent',
    enum: AgentStatusEnum,
    example: AgentStatusEnum.APPROVED,
  })
  status: AgentStatusEnum;

  /**
   * Thời gian tạo
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp millis)',
    example: 1682506892000,
  })
  createdAt: number;

  /**
   * Trạng thái active của agent base
   */
  @ApiProperty({
    description: 'Trạng thái active của agent base',
    example: true,
  })
  active: boolean;
}

/**
 * DTO cho việc trả về thông tin chi tiết agent base
 */
export class AgentBaseResponseDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  name: string;

  /**
   * URL avatar của agent
   */
  @ApiPropertyOptional({
    description: 'URL avatar của agent',
    example: 'https://example.com/system-assistant.png',
  })
  avatar: string | null;

  /**
   * Cấu hình model AI
   */
  @ApiProperty({
    description: 'Cấu hình model AI',
    type: ModelConfigResponseDto,
  })
  modelConfig: ModelConfigResponseDto;

  /**
   * Thông tin model được sử dụng
   */
  @ApiPropertyOptional({
    description: 'Thông tin model được sử dụng',
    type: ModelInfoDto,
  })
  model?: ModelInfoDto;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  instruction: string | null;

  /**
   * Thông tin vector store
   */
  @ApiPropertyOptional({
    description: 'Thông tin vector store',
    type: VectorStoreDto,
  })
  vector?: VectorStoreDto | null;

  /**
   * Trạng thái của agent
   */
  @ApiProperty({
    description: 'Trạng thái của agent',
    enum: AgentStatusEnum,
    example: AgentStatusEnum.APPROVED,
  })
  status: AgentStatusEnum;

  /**
   * Trạng thái active của agent base
   */
  @ApiProperty({
    description: 'Trạng thái active của agent base',
    example: true,
  })
  active: boolean;

  /**
   * Thông tin người tạo
   */
  @ApiPropertyOptional({
    description: 'Thông tin người tạo',
    type: EmployeeInfoDto,
  })
  created?: EmployeeInfoDto;

  /**
   * Thông tin người cập nhật
   */
  @ApiPropertyOptional({
    description: 'Thông tin người cập nhật',
    type: EmployeeInfoDto,
  })
  updated?: EmployeeInfoDto;

  // Đã loại bỏ trường deleted theo yêu cầu

  /**
   * URL tải lên avatar (nếu có)
   */
  @ApiPropertyOptional({
    description: 'URL tải lên avatar (nếu có)',
    example: 'https://example.com/avatar-upload-url',
    nullable: true,
  })
  avatarUrlUpload?: string;

  /**
   * Danh sách quan hệ multi-agent
   */
  @ApiPropertyOptional({
    description: 'Danh sách quan hệ multi-agent',
    type: [MultiAgentRelationResponseDto],
  })
  multiAgentRelations?: MultiAgentRelationResponseDto[];

  /**
   * Chuyển đổi từ entity sang DTO
   * @param agentBase Entity AgentBase
   * @param agent Entity Agent
   * @param avatarUrlUpload URL tải lên avatar (nếu có)
   * @param cdnService
   * @param employeeInfoService Service để lấy thông tin nhân viên
   * @param multiAgentRelations Danh sách quan hệ multi-agent

   * @returns AgentBaseResponseDto
   */
  static async fromEntity(
    agentBase: AgentBase,
    agent?: Agent | null,
    avatarUrlUpload?: string,
    cdnService?: CdnService,
    employeeInfoService?: any,
    multiAgentRelations?: MultiAgentRelationResponseDto[],
  ): Promise<AgentBaseResponseDto> {
    const dto = new AgentBaseResponseDto();

    if (agent) {
      dto.id = agent.id;
      dto.name = agent.name;
      dto.avatar = agent.avatar && cdnService
        ? AvatarUrlHelper.generateViewUrl(cdnService, agent.avatar)
        : null;
      dto.instruction = agent.instruction;

      // Cấu hình model
      if (agent.modelConfig) {

        dto.modelConfig = {
          ...agent.modelConfig,
        };
      }

      // Vector store
      if (agent.vectorStoreId) {
        dto.vector = {
          vectorStoreId: agent.vectorStoreId,
          vectorStoreName: 'Vector Store', // Cần lấy tên thực tế từ service khác
        };
      }
    }

    if (agentBase) {
      dto.active = agentBase.active || false;

      // Thông tin người tạo, cập nhật
      if (employeeInfoService) {
        try {
          if (agentBase.createdBy) {
            const createdByInfo = await employeeInfoService.getEmployeeInfo(agentBase.createdBy);
            dto.created = {
              employeeId: agentBase.createdBy,
              name: createdByInfo?.name || 'Unknown',
              avatar: createdByInfo?.avatar || null,
              date: agent?.createdAt, // Thêm thời gian tạo từ agent.createdAt
            };
          }

          if (agentBase.updatedBy) {
            const updatedByInfo = await employeeInfoService.getEmployeeInfo(agentBase.updatedBy);
            dto.updated = {
              employeeId: agentBase.updatedBy,
              name: updatedByInfo?.name || 'Unknown',
              avatar: updatedByInfo?.avatar || null,
              date: agent?.updatedAt, // Thêm thời gian cập nhật từ agent.updatedAt
            };
          }

          // Đã loại bỏ phần lấy thông tin người xóa
        } catch (error) {
          console.warn(`Không thể lấy thông tin nhân viên: ${error.message}`);
        }
      }
    }

    if (avatarUrlUpload) {
      dto.avatarUrlUpload = avatarUrlUpload;
    }

    // Thêm thông tin quan hệ multi-agent nếu có
    if (multiAgentRelations && multiAgentRelations.length > 0) {
      dto.multiAgentRelations = multiAgentRelations;
    }

    return dto;
  }
}

/**
 * DTO cho việc trả về thông tin agent base đã xóa trong danh sách trash
 */
export class AgentBaseTrashItemDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  name: string;

  /**
   * URL avatar của agent
   */
  @ApiPropertyOptional({
    description: 'URL avatar của agent',
    example: 'https://example.com/system-assistant.png',
  })
  avatar: string | null;

  /**
   * Tên model sử dụng
   */
  @ApiProperty({
    description: 'Tên model sử dụng',
    example: 'gpt-4o',
  })
  model: string;

  /**
   * Model ID từ provider
   */
  @ApiPropertyOptional({
    description: 'Model ID từ provider',
    example: 'gpt-4o',
  })
  model_id?: string | null;

  /**
   * Loại provider
   */
  @ApiPropertyOptional({
    description: 'Loại provider',
    example: 'OPENAI',
  })
  type_provider?: string | null;

  /**
   * Trạng thái của agent
   */
  @ApiProperty({
    description: 'Trạng thái của agent',
    enum: AgentStatusEnum,
    example: AgentStatusEnum.APPROVED,
  })
  status: AgentStatusEnum;

  /**
   * Thời gian xóa
   */
  @ApiPropertyOptional({
    description: 'Thời gian xóa (timestamp millis)',
    example: 1682506892000,
  })
  deletedAt?: number;

  /**
   * Thông tin người xóa
   */
  @ApiPropertyOptional({
    description: 'Thông tin người xóa',
    type: EmployeeInfoDto,
  })
  deleted?: EmployeeInfoDto;
}


