import { AppException, ErrorCode } from '@common/exceptions/app.exception';

/**
 * Enum định nghĩa các loại MIME cho file
 */
export enum FileJsonlTypeEnum {
  JSONL = 'application/jsonl',
}

/**
 * Object tiện ích để làm việc với FileJsonlTypeEnum
 */
export const FileJsonlType = {
  /**
   * Lấy giá trị chuỗi của một loại file
   * @param type Loại file
   * @returns Giá trị MIME tương ứng
   */
  getValue(type: FileJsonlTypeEnum): string {
    return type;
  },

  /**
   * Lấy enum FileJsonlTypeEnum từ tên loại file hoặc giá trị MIME type
   * @param type Tên loại file (key của enum) hoặc giá trị MIME type (ví dụ: 'application/jsonl')
   * @returns Giá trị enum FileJsonlTypeEnum tương ứng
   * @throws AppException nếu loại file không tồn tại
   */
  getMimeType(type: string): FileJsonlTypeEnum {
    // Kiểm tra nếu là key của enum (ví dụ: 'JSONL')
    const mimeTypeFromKey = FileJsonlTypeEnum[type as keyof typeof FileJsonlTypeEnum];
    if (mimeTypeFromKey) {
      return mimeTypeFromKey;
    }

    // Kiểm tra nếu là giá trị MIME type (ví dụ: 'application/jsonl')
    const entries = Object.entries(FileJsonlTypeEnum);
    const entry = entries.find(([_, value]) => value === type);

    if (entry) {
      return FileJsonlTypeEnum[entry[0] as keyof typeof FileJsonlTypeEnum];
    }

    // Nếu không tìm thấy, ném exception
    throw new AppException(
      ErrorCode.FILE_TYPE_NOT_FOUND,
      `Loại tệp '${type}' không được hỗ trợ`
    );
  },
};



