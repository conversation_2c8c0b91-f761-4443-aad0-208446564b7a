import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@common/exceptions';
import { MARKETPLACE_ERROR_CODES } from '@modules/marketplace/exceptions';
import { ProductCategory } from '@modules/marketplace/enums';
import { Product } from '@modules/marketplace/entities';

// Import repositories
import { KnowledgeFileRepository } from '@modules/data/knowledge-files/repositories';
import { AgentRepository } from '@modules/agent/repositories';
import { UserDataFineTuneRepository, AdminDataFineTuneRepository } from '@modules/models/repositories';

// Import services
import { S3Service } from '@shared/services/s3.service';

// Import entities
import { UserDataFineTune, AdminDataFineTune } from '@modules/models/entities';

// Import enums and types
import { OwnerType } from '@shared/enums';
import { KnowledgeFileStatus } from '@modules/data/knowledge-files/enums/knowledge-file-status.enum';
import { DataFineTuneStatus } from '@modules/models/constants/data-fine-tune-status.enum';

/**
 * Service xử lý logic chia sẻ tài nguyên sau khi mua sản phẩm
 */
@Injectable()
export class ResourceSharingService {
  private readonly logger = new Logger(ResourceSharingService.name);

  constructor(
    private readonly knowledgeFileRepository: KnowledgeFileRepository,
    private readonly agentRepository: AgentRepository,
    private readonly userDataFineTuneRepository: UserDataFineTuneRepository,
    private readonly adminDataFineTuneRepository: AdminDataFineTuneRepository,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Xử lý chia sẻ tài nguyên cho người mua sau khi thanh toán thành công
   * @param userId ID người mua
   * @param products Danh sách sản phẩm đã mua
   */
  async shareResourcesAfterPurchase(userId: number, products: Product[]): Promise<void> {
    this.logger.log(`Bắt đầu chia sẻ tài nguyên cho user ${userId} với ${products.length} sản phẩm`);

    let successCount = 0;
    let errorCount = 0;

    for (const product of products) {
      try {
        await this.shareResourceForProduct(userId, product);
        successCount++;
        this.logger.log(`✅ Thành công chia sẻ tài nguyên cho sản phẩm ${product.id} (${product.name})`);
      } catch (error) {
        errorCount++;
        this.logger.error(
          `❌ Lỗi khi chia sẻ tài nguyên cho sản phẩm ${product.id} (${product.name}): ${error.message}`,
          error.stack
        );
        // Không throw error để không làm fail toàn bộ transaction thanh toán
        // Log để admin có thể xử lý thủ công sau
      }
    }

    this.logger.log(`Hoàn thành chia sẻ tài nguyên cho user ${userId}: ${successCount} thành công, ${errorCount} lỗi`);
  }

  /**
   * Chia sẻ tài nguyên cho một sản phẩm cụ thể
   * @param userId ID người mua
   * @param product Sản phẩm đã mua
   */
  private async shareResourceForProduct(userId: number, product: Product): Promise<void> {
    this.logger.debug(`Kiểm tra sản phẩm ${product.id}: name="${product.name}", category="${product.category}", sourceId="${product.sourceId}"`);

    if (!product.sourceId) {
      this.logger.warn(`Sản phẩm ${product.id} (${product.name}) không có source_id, bỏ qua chia sẻ tài nguyên`);
      this.logger.warn(`Chi tiết sản phẩm: category=${product.category}, userId=${product.userId}, employeeId=${product.employeeId}`);
      return;
    }

    this.logger.log(`Chia sẻ tài nguyên cho sản phẩm ${product.id} (${product.name}), category: ${product.category}, sourceId: ${product.sourceId}`);

    switch (product.category) {
      case ProductCategory.KNOWLEDGE_FILE:
        await this.shareKnowledgeFile(userId, product.sourceId);
        break;
      
      case ProductCategory.FINETUNE:
        await this.shareFineTuneData(userId, product.sourceId, product);
        break;
      
      default:
        this.logger.warn(`Không hỗ trợ chia sẻ tài nguyên cho category: ${product.category}`);
    }
  }

  /**
   * Chia sẻ Knowledge File (chỉ đọc) với S3 copy
   * @param userId ID người mua
   * @param sourceFileId ID file gốc
   */
  private async shareKnowledgeFile(userId: number, sourceFileId: string): Promise<void> {
    this.logger.debug(`Chia sẻ knowledge file ${sourceFileId} cho user ${userId}`);

    // 1. Lấy thông tin file gốc
    const sourceFile = await this.knowledgeFileRepository.findOne({
      where: { id: sourceFileId }
    });

    if (!sourceFile) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.RESOURCE_NOT_FOUND,
        `Không tìm thấy knowledge file gốc: ${sourceFileId}`
      );
    }

    // 2. Kiểm tra file có đang được bán không
    if (!sourceFile.isForSale) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.RESOURCE_NOT_FOR_SALE,
        `Knowledge file ${sourceFileId} không được đăng bán`
      );
    }

    // 3. Kiểm tra user đã có file này chưa
    const existingFile = await this.knowledgeFileRepository.findOne({
      where: {
        sourceId: sourceFileId,
        ownedBy: userId,
        ownerType: OwnerType.USER
      }
    });

    if (existingFile) {
      this.logger.warn(`User ${userId} đã có knowledge file từ source ${sourceFileId}`);
      return;
    }

    // 4. Tạo storage key unique cho user
    const timestamp = Date.now();
    const originalFileName = sourceFile.storageKey.split('/').pop();
    const sharedStorageKey = `shared/${userId}/${timestamp}-${originalFileName}`;

    // 5. Copy file thật với HeadObject check trước
    let finalStorageKey = sharedStorageKey;
    let copySuccess = false;

    try {
      // Bước 1: Kiểm tra file nguồn có tồn tại không bằng HeadObject
      this.logger.debug(`Kiểm tra file nguồn tồn tại: ${sourceFile.storageKey}`);
      const fileExists = await this.s3Service.checkObjectExists(sourceFile.storageKey);

      if (!fileExists) {
        throw new Error(`File nguồn không tồn tại: ${sourceFile.storageKey}`);
      }

      this.logger.debug(`✅ File nguồn tồn tại, tiến hành copy`);

      // Bước 2: Copy file từ nguồn sang destination
      this.logger.debug(`Copy file từ ${sourceFile.storageKey} sang ${sharedStorageKey}`);
      const copyResult = await this.s3Service.copyFile(sourceFile.storageKey, sharedStorageKey);
      this.logger.log(`✅ Copy file S3 thành công: ${sharedStorageKey} (ETag: ${copyResult.etag})`);
      copySuccess = true;

    } catch (error) {
      this.logger.error(`❌ Không thể copy file S3: ${error.message}`);
      this.logger.warn(`Tạo placeholder file thay vì dùng key gốc để tránh conflict: ${sharedStorageKey}`);
      // Tạo placeholder file với unique key để tránh conflict
      await this.createPlaceholderFile(sharedStorageKey, 'application/pdf');
      finalStorageKey = sharedStorageKey; // Dùng unique key với placeholder file
      copySuccess = false;
    }

    // 6. Tạo bản sao cho user với thông tin copy status
    const fileName = copySuccess ?
      `${sourceFile.name}` :
      `[Đã mua - Shared] ${sourceFile.name}`;

    const sharedFile = this.knowledgeFileRepository.create({
      name: fileName,
      storageKey: finalStorageKey, // Unique key nếu copy thành công, hoặc original key nếu fallback
      ownerType: OwnerType.USER,
      ownedBy: userId,
      isOwner: false, // Không phải chủ sở hữu
      isForSale: false, // Không được bán lại
      storage: sourceFile.storage,
      fileId: sourceFile.fileId, // Dùng chung fileId để trỏ đến cùng file trên OpenAI
      status: KnowledgeFileStatus.APPROVED, // Tự động approve
      sourceId: sourceFileId,
    });

    try {
      await this.knowledgeFileRepository.save(sharedFile);
      this.logger.log(`Đã chia sẻ knowledge file ${sourceFileId} cho user ${userId} với ID: ${sharedFile.id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi lưu shared knowledge file: ${error.message}`, error.stack);
      throw new AppException(
        MARKETPLACE_ERROR_CODES.RESOURCE_SHARING_FAILED,
        `Không thể chia sẻ knowledge file: ${error.message}`
      );
    }
  }



  /**
   * Chia sẻ Fine-tune Data (chỉ đọc) với copy data
   * @param userId ID người mua
   * @param sourceDataId ID data gốc
   * @param product Thông tin sản phẩm để xác định nguồn
   */
  private async shareFineTuneData(userId: number, sourceDataId: string, product: Product): Promise<void> {
    this.logger.debug(`Chia sẻ fine-tune data ${sourceDataId} cho user ${userId}`);

    // Xác định nguồn dữ liệu dựa trên product
    let sourceData: UserDataFineTune | AdminDataFineTune | null = null;

    if (product.userId) {
      // Sản phẩm của user - lấy từ user_data_fine_tune
      sourceData = await this.userDataFineTuneRepository.findOne({
        where: { id: sourceDataId }
      });
    } else if (product.employeeId) {
      // Sản phẩm của admin - lấy từ admin_data_fine_tune
      sourceData = await this.adminDataFineTuneRepository.findOne({
        where: { id: sourceDataId }
      });
    }

    if (!sourceData) {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.RESOURCE_NOT_FOUND,
        `Không tìm thấy fine-tune data gốc: ${sourceDataId}`
      );
    }

    // 2. Kiểm tra user đã có data này chưa
    let whereCondition: any = { userId: userId };

    if (product.userId) {
      whereCondition.sourceUserId = sourceDataId;
    } else if (product.employeeId) {
      whereCondition.sourceAdminId = sourceDataId;
    }

    const existingData = await this.userDataFineTuneRepository.findOne({
      where: whereCondition
    });

    if (existingData) {
      this.logger.warn(`User ${userId} đã có fine-tune data từ source ${sourceDataId}`);
      return;
    }

    // 3. Copy S3 files với unique keys
    const timestamp = Date.now();
    let finalTrainDataset: string;
    let finalValidDataset: string | null = null;

    // Copy train dataset
    const originalTrainFileName = sourceData.trainDataset.split('/').pop();
    const sharedTrainKey = `shared/${userId}/${timestamp}-train-${originalTrainFileName}`;

    try {
      this.logger.debug(`Copy train dataset từ ${sourceData.trainDataset} sang ${sharedTrainKey}`);
      const trainExists = await this.s3Service.checkObjectExists(sourceData.trainDataset);

      if (trainExists) {
        await this.s3Service.copyFile(sourceData.trainDataset, sharedTrainKey);
        finalTrainDataset = sharedTrainKey;
        this.logger.log(`✅ Copy train dataset thành công: ${sharedTrainKey}`);
      } else {
        this.logger.warn(`Train dataset không tồn tại, tạo placeholder file: ${sharedTrainKey}`);
        // Tạo placeholder file với unique key để tránh conflict
        await this.createPlaceholderFile(sharedTrainKey, 'application/jsonl');
        finalTrainDataset = sharedTrainKey;
      }
    } catch (error) {
      this.logger.error(`❌ Lỗi copy train dataset!: ${error.message}`);
      // Tạo placeholder file với unique key để tránh conflict
      await this.createPlaceholderFile(sharedTrainKey, 'application/jsonl');
      finalTrainDataset = sharedTrainKey;
    }

    // Copy valid dataset nếu có
    if (sourceData.validDataset) {
      const originalValidFileName = sourceData.validDataset.split('/').pop();
      const sharedValidKey = `shared/${userId}/${timestamp}-valid-${originalValidFileName}`;

      try {
        this.logger.debug(`Copy valid dataset từ ${sourceData.validDataset} sang ${sharedValidKey}`);
        const validExists = await this.s3Service.checkObjectExists(sourceData.validDataset);

        if (validExists) {
          await this.s3Service.copyFile(sourceData.validDataset, sharedValidKey);
          finalValidDataset = sharedValidKey;
          this.logger.log(`✅ Copy valid dataset thành công: ${sharedValidKey}`);
        } else {
          this.logger.warn(`Valid dataset không tồn tại, tạo placeholder file: ${sharedValidKey}`);
          // Tạo placeholder file với unique key để tránh conflict
          await this.createPlaceholderFile(sharedValidKey, 'application/jsonl');
          finalValidDataset = sharedValidKey;
        }
      } catch (error) {
        this.logger.error(`❌ Lỗi copy valid dataset: ${error.message}`);
        // Tạo placeholder file với unique key để tránh conflict
        await this.createPlaceholderFile(sharedValidKey, 'application/jsonl');
        finalValidDataset = sharedValidKey;
      }
    }

    // 4. Tạo bản sao UserDataFineTuning với unique S3 keys
    let sharedUserData: any;

    if (product.userId) {
      // Source từ user - cast để có type safety
      const userData = sourceData as UserDataFineTune;
      sharedUserData = this.userDataFineTuneRepository.create({
        name: `${userData.name}`,
        description: userData.description,
        trainDataset: finalTrainDataset, // Unique S3 key
        validDataset: finalValidDataset, // Unique S3 key hoặc null
        userId: userId,
        provider: userData.provider,
        status: DataFineTuneStatus.PENDING,
        sourceUserId: sourceDataId,
        sourceAdminId: null
      });
    } else if (product.employeeId) {
      // Source từ admin - cast để có type safety
      const adminData = sourceData as AdminDataFineTune;
      sharedUserData = this.userDataFineTuneRepository.create({
        name: `${adminData.name}`,
        description: adminData.description,
        trainDataset: finalTrainDataset, // Unique S3 key
        validDataset: finalValidDataset, // Unique S3 key hoặc null
        userId: userId,
        provider: adminData.provider,
        status: DataFineTuneStatus.PENDING,
        sourceUserId: null,
        sourceAdminId: sourceDataId
      });
    } else {
      throw new AppException(
        MARKETPLACE_ERROR_CODES.RESOURCE_SHARING_FAILED,
        'Không thể xác định loại source data'
      );
    }

    try {
      // Lưu UserDataFineTuning
      await this.userDataFineTuneRepository.save(sharedUserData);
      this.logger.log(`Đã chia sẻ fine-tune data ${sourceDataId} cho user ${userId} với ID: ${sharedUserData.id}`);
    } catch (error) {
      this.logger.error(`Lỗi khi lưu shared fine-tune data: ${error.message}`, error.stack);
      throw new AppException(
        MARKETPLACE_ERROR_CODES.RESOURCE_SHARING_FAILED,
        `Không thể chia sẻ fine-tune data: ${error.message}`
      );
    }
  }

  /**
   * Kiểm tra sản phẩm có thể mua không (kiểm tra is_for_sale của tài nguyên gốc)
   * @param product Sản phẩm cần kiểm tra
   * @returns true nếu có thể mua
   */
  async validateProductCanBePurchased(product: Product): Promise<boolean> {
    if (!product.sourceId) {
      // Sản phẩm không có source_id thì có thể mua (sản phẩm gốc)
      return true;
    }

    this.logger.debug(`Kiểm tra tài nguyên gốc có thể mua: ${product.sourceId}, category: ${product.category}`);

    switch (product.category) {
      case ProductCategory.KNOWLEDGE_FILE:
        return await this.checkKnowledgeFileCanBePurchased(product.sourceId);

      case ProductCategory.AGENT:
        return await this.checkAgentCanBePurchased(product.sourceId);

      case ProductCategory.FINETUNE:
        return await this.checkFineTuneDataCanBePurchased(product.sourceId, product);

      default:
        this.logger.warn(`Không hỗ trợ kiểm tra category: ${product.category}`);
        return false;
    }
  }

  private async checkKnowledgeFileCanBePurchased(sourceId: string): Promise<boolean> {
    const file = await this.knowledgeFileRepository.findOne({
      where: { id: sourceId },
      select: ['isForSale']
    });
    return file?.isForSale || false;
  }

  private async checkAgentCanBePurchased(sourceId: string): Promise<boolean> {
    const agent = await this.agentRepository.findOne({
      where: { id: sourceId },
      select: ['isForSale']
    });
    return agent?.isForSale || false;
  }

  private async checkFineTuneDataCanBePurchased(sourceId: string, product: Product): Promise<boolean> {
    try {
      this.logger.debug(`Kiểm tra fine-tune data có thể mua: sourceId=${sourceId}, product.userId=${product.userId}, product.employeeId=${product.employeeId}`);

      // Kiểm tra sản phẩm thuộc về user hay admin
      if (product.userId) {
        // Sản phẩm của user - kiểm tra trong user_data_fine_tune
        const userData = await this.userDataFineTuneRepository.findOne({
          where: { id: sourceId },
          select: ['id', 'status']
        });

        if (!userData) {
          this.logger.warn(`Không tìm thấy user data fine tuning với ID: ${sourceId}`);
          return false;
        }

        // User data fine tune được coi là có thể mua nếu status = APPROVED
        const canBePurchased = userData.status === DataFineTuneStatus.APPROVED;
        this.logger.debug(`User fine-tune data status: ${userData.status}, can purchase: ${canBePurchased}`);
        return canBePurchased;

      } else if (product.employeeId) {
        // Sản phẩm của admin - kiểm tra trong admin_data_fine_tune
        const adminData = await this.adminDataFineTuneRepository.findOne({
          where: { id: sourceId },
          select: ['id'] // Admin data luôn có thể mua
        });

        if (!adminData) {
          this.logger.warn(`Không tìm thấy admin data fine tuning với ID: ${sourceId}`);
          return false;
        }

        this.logger.debug(`Admin fine-tune data found, can purchase: true`);
        return true;

      } else {
        this.logger.warn(`Sản phẩm ${product.id} không có userId hoặc employeeId`);
        return false;
      }

    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra fine-tune data: ${error.message}`);
      return false;
    }
  }

  /**
   * Tạo file placeholder rỗng cho trường hợp file gốc không tồn tại
   * @param key S3 key để tạo file placeholder
   * @param contentType MIME type của file
   */
  private async createPlaceholderFile(key: string, contentType: string = 'application/jsonl'): Promise<void> {
    try {
      const placeholderContent = JSON.stringify({
        error: 'File gốc không tồn tại',
        message: 'Đây là file placeholder được tạo tự động',
        created_at: new Date().toISOString()
      });

      await this.s3Service.uploadFile(key, Buffer.from(placeholderContent), contentType);
      this.logger.log(`✅ Tạo placeholder file thành công: ${key}`);
    } catch (error) {
      this.logger.error(`❌ Lỗi tạo placeholder file ${key}: ${error.message}`);
      // Không throw error để không làm fail toàn bộ quá trình sharing
    }
  }
}
