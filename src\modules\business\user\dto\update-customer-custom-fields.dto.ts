import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { MetadataFieldDto, MetadataFieldResponseDto } from './metadata-field.dto';

/**
 * DTO cho việc cập nhật trường tùy chỉnh của khách hàng chuyển đổi
 */
export class UpdateCustomerCustomFieldsDto {
  /**
   * Metadata - Dữ liệu trường tùy chỉnh với configId và giá trị
   * @example [{ "configId": "customer_age", "value": "25" }, { "configId": "customer_interest", "value": ["sports", "music"] }]
   */
  @ApiProperty({
    description: 'Metadata - Dữ liệu trường tùy chỉnh với configId và giá trị',
    type: [MetadataFieldDto],
    required: false,
    example: [
      {
        configId: 'day_of_birth',
        value: '28/11/2003'
      },
      {
        configId: 'haianh',
        value: 'Thông tin bổ sung'
      },
      {
        configId: 'product_color',
        value: 'RED12345678'
      }
    ],
  })
  @IsOptional()
  @IsArray({ message: 'Metadata phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => MetadataFieldDto)
  metadata?: MetadataFieldDto[];
}

/**
 * DTO response cho trường tùy chỉnh của khách hàng chuyển đổi
 */
export class CustomerCustomFieldsResponseDto {
  @ApiProperty({
    description: 'ID khách hàng chuyển đổi',
    example: 123,
  })
  id: number;

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh với thông tin chi tiết',
    type: [MetadataFieldResponseDto],
    example: [
      {
        configId: 'day_of_birth',
        value: '28/11/2003',
        label: 'Ngày sinh',
        type: 'text',
        required: true
      },
      {
        configId: 'haianh',
        value: 'Thông tin bổ sung',
        label: 'Hải Anh',
        type: 'text',
        required: false
      }
    ],
  })
  customFields: MetadataFieldResponseDto[];

  @ApiProperty({
    description: 'Thời gian cập nhật cuối',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;
}
