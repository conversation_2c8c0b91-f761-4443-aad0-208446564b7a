import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOptionsWhere, ILike, Repository, Raw } from 'typeorm';
import { UserTemplateEmail } from '../entities/user-template-email.entity';
import { PaginatedResult } from '@/common/response';
import { TemplateEmailQueryDto } from '../dto/template-email';

/**
 * Repository cho UserTemplateEmail
 */
@Injectable()
export class UserTemplateEmailRepository {
  constructor(
    @InjectRepository(UserTemplateEmail)
    public readonly repository: Repository<UserTemplateEmail>,
  ) {}

  /**
   * Tìm kiếm nhiều template email với phân trang và filter
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách template email với phân trang
   */
  async findWithPagination(
    userId: number,
    queryDto: TemplateEmailQueryDto,
  ): Promise<PaginatedResult<UserTemplateEmail>> {
    const { page, limit, search, sortBy, sortDirection, name, tag, status } = queryDto;
    const skip = (page - 1) * limit;

    // Xây dựng điều kiện tìm kiếm
    const where: FindOptionsWhere<UserTemplateEmail> = { userId };

    if (search) {
      where.name = ILike(`%${search}%`);
    }

    if (name) {
      where.name = ILike(`%${name}%`);
    }

    if (status) {
      where.status = status;
    }

    // Xây dựng options tìm kiếm
    const options: FindManyOptions<UserTemplateEmail> = {
      where,
      skip,
      take: limit,
      order: {
        [sortBy || 'createdAt']: sortDirection || 'DESC',
      },
    };

    // Nếu có tag, cần xử lý đặc biệt vì tags là một mảng trong JSONB
    if (tag) {
      options.where = [
        {
          ...where,
          tags: Raw((alias) => `${alias} @> :tag`, { tag: JSON.stringify([tag]) }),
        },
      ];
    }

    // Thực hiện truy vấn
    const [items, totalItems] = await this.repository.findAndCount(options);

    // Tính toán thông tin phân trang
    const totalPages = Math.ceil(totalItems / limit);

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
      },
    };
  }

  /**
   * Tìm template email theo ID
   * @param id ID của template
   * @param userId ID của người dùng (để kiểm tra quyền sở hữu)
   * @returns Template email
   */
  async findById(id: number, userId: number): Promise<UserTemplateEmail> {
    const template = await this.repository.findOne({
      where: { id, userId },
    });

    if (!template) {
      throw new NotFoundException(`Template email với ID ${id} không tồn tại hoặc không thuộc về người dùng này`);
    }

    return template;
  }

  /**
   * Tạo mới template email
   * @param data Dữ liệu template email
   * @returns Template email đã tạo
   */
  async create(data: Partial<UserTemplateEmail>): Promise<UserTemplateEmail> {
    const template = this.repository.create(data);
    return this.repository.save(template);
  }

  /**
   * Cập nhật template email
   * @param id ID của template
   * @param userId ID của người dùng (để kiểm tra quyền sở hữu)
   * @param data Dữ liệu cập nhật
   * @returns Template email đã cập nhật
   */
  async update(id: number, userId: number, data: Partial<UserTemplateEmail>): Promise<UserTemplateEmail> {
    // Kiểm tra template tồn tại và thuộc về người dùng
    await this.findById(id, userId);

    // Cập nhật template
    await this.repository.update({ id, userId }, data);

    // Trả về template đã cập nhật
    return this.findById(id, userId);
  }

  /**
   * Xóa template email
   * @param id ID của template
   * @param userId ID của người dùng (để kiểm tra quyền sở hữu)
   * @returns true nếu xóa thành công
   */
  async delete(id: number, userId: number): Promise<boolean> {
    // Kiểm tra template tồn tại và thuộc về người dùng
    await this.findById(id, userId);

    // Xóa template
    const result = await this.repository.delete({ id, userId });

    return result && result?.affected !== null && result?.affected !== undefined && result?.affected > 0;
  }

  /**
   * Xóa nhiều template email
   * @param ids Danh sách ID của các template
   * @param userId ID của người dùng (để kiểm tra quyền sở hữu)
   * @returns Kết quả xóa với danh sách thành công và thất bại
   */
  async deleteMultiple(ids: number[], userId: number): Promise<{
    deletedIds: number[];
    failedIds: Array<{ id: number; reason: string }>;
  }> {
    const deletedIds: number[] = [];
    const failedIds: Array<{ id: number; reason: string }> = [];

    // Xử lý từng ID một cách tuần tự để có thể xử lý lỗi riêng biệt
    for (const id of ids) {
      try {
        const success = await this.delete(id, userId);
        if (success) {
          deletedIds.push(id);
        } else {
          failedIds.push({ id, reason: 'Không thể xóa template' });
        }
      } catch (error) {
        let reason = 'Lỗi không xác định';
        if (error.message?.includes('không tồn tại')) {
          reason = 'Template không tồn tại hoặc không có quyền truy cập';
        } else if (error.message) {
          reason = error.message;
        }
        failedIds.push({ id, reason });
      }
    }

    return { deletedIds, failedIds };
  }

  /**
   * Đếm số lượng template email
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng template email
   */
  async count(options?: FindManyOptions<UserTemplateEmail>): Promise<number> {
    return this.repository.count(options);
  }

  /**
   * Tìm kiếm nhiều template email
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách template email
   */
  async find(options?: FindManyOptions<UserTemplateEmail>): Promise<UserTemplateEmail[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một template email
   * @param options Tùy chọn tìm kiếm
   * @returns Template email hoặc null
   */
  async findOne(options?: FindManyOptions<UserTemplateEmail>): Promise<UserTemplateEmail | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }
}
