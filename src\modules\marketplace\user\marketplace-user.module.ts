import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { ProductUserController, CartUserController, OrderUserController, PaymentController  } from './controllers';
import {
  ProductUserService,
  CartUserService,
  OrderUserService,
  PaymentService
} from './services';
import { Product, Cart, CartItem, MarketOrder, MarketOrderLine } from '../entities';
import { ProductRepository, CartRepository, CartItemRepository, MarketOrderRepository, MarketOrderLineRepository } from '../repositories';
import { S3Service } from '@shared/services/s3.service';
import { RedisService } from '@shared/services/redis.service';
import { CdnService } from '@shared/services/cdn.service';
import { SystemConfigurationModule } from '@modules/system-configuration/system-configuration.module';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { User } from '@modules/user/entities';
import { ProductHelper, ValidationHelper, CartHelper, PurchaseHistoryHelper } from '../helpers';
// Import entities và repositories cho resource sharing
import { KnowledgeFile } from '@modules/data/knowledge-files/entities';
import { Agent } from '@modules/agent/entities';
import { UserDataFineTune, AdminDataFineTune } from '@modules/models/entities';
import { StrategyAgent } from '@modules/strategy/entities';
import { KnowledgeFileRepository } from '@modules/data/knowledge-files/repositories';
import { AgentRepository } from '@modules/agent/repositories';
import { UserDataFineTuneRepository, AdminDataFineTuneRepository } from '@modules/models/repositories';
import { StrategyAgentRepository } from '@modules/strategy/repositories';
// Import ResourceSharingService
import { ResourceSharingService } from '../shares/resource-sharing.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Product, Cart, CartItem, MarketOrder, MarketOrderLine, User,
      KnowledgeFile, Agent, UserDataFineTune, AdminDataFineTune, StrategyAgent
    ]),
    ScheduleModule.forRoot(),
    SystemConfigurationModule,
  ],
  controllers: [ProductUserController, CartUserController, OrderUserController, PaymentController],
  providers: [
    ProductUserService,
    CartUserService,
    OrderUserService,
    PaymentService,
    ProductRepository,
    CartRepository,
    CartItemRepository,
    MarketOrderRepository,
    MarketOrderLineRepository,
    UserRepository,
    KnowledgeFileRepository,
    AgentRepository,
    UserDataFineTuneRepository,
    AdminDataFineTuneRepository,
    StrategyAgentRepository,
    S3Service,
    RedisService,
    CdnService,
    ProductHelper,
    ValidationHelper,
    CartHelper,
    PurchaseHistoryHelper,
    ResourceSharingService
  ],
  exports: [ProductUserService, CartUserService, OrderUserService, PaymentService],
})
export class MarketplaceUserModule {}
