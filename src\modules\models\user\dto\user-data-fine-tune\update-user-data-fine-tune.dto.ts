import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho việc tạo mới user data fine tune
 */
export class UpdateUserDataFineTuneDto {
    /**
     * Tên của bộ dữ liệu fine-tune 
     */
    @ApiProperty({
        description: 'Tên của bộ dữ liệu fine-tune',
        example: 'Customer Support Dataset',
        maxLength: 255,
    })
    @IsString()
    @IsNotEmpty()
    @MaxLength(255)
    name?: string;

    /**
     * Mô tả nội dung bộ dữ liệu
     */
    @ApiPropertyOptional({
        description: 'Mô tả nội dung bộ dữ liệu',
        example: 'Dataset chứa các cuộc hội thoại hỗ trợ khách hàng',
    })
    @IsOptional()
    @IsString()
    description?: string;

    /**
     * Tập dữ liệu huấn luyện, định dạng JSON
     */
    @ApiProperty({
        description: 'Tập dữ liệu huấn luyệ<PERSON>, định dạng JSON',
        example: 'application/jsonl'
    })
    @IsArray()
    @IsNotEmpty()
    trainDataset?: string;

    /**
     * Tập dữ liệu validation, định dạng JSON (nếu có)
     */
    @ApiPropertyOptional({
        description: 'Tập dữ liệu validation, định dạng JSON (nếu có)',
        example: 'application/jsonl'
    })
    @IsOptional()
    @IsArray()
    validDataset?: string;
}
