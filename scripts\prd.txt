# Product Requirements Document - Fix TypeScript Errors in RedAI V2 Backend

## Mô tả dự án
Sửa các lỗi TypeScript trong ứng dụng RedAI V2 Backend khi chạy `npm run start:dev`. Có 146 lỗi cần đ<PERSON><PERSON><PERSON>, chủ yếu tập trung vào 2 modules: Agent và Models.

## Business Objectives
- Enable users to configure and manage shipping provider integrations
- Provide secure storage of sensitive API credentials using AES-256-GCM encryption
- Implement test connection functionality for all supported providers
- Support multiple shipping providers with provider-specific validation
- Ensure data security with masked responses and encrypted storage

## Target Users
- Business users who need to integrate with shipping providers
- Developers who need to test shipping provider connections
- System administrators managing shipping configurations

## Core Features

### 1. Provider Configuration Management
- Create, read, update, delete shipping provider configurations
- Support for 4 providers: GHN, GHTK, Ahamove, J&T Express
- Provider-specific validation and configuration schemas
- Prevent duplicate configurations per provider per user

### 2. Security & Encryption
- AES-256-GCM encryption for sensitive data storage
- Environment variable-based encryption key management
- Sensitive data masking in API responses
- Secure credential validation

### 3. Connection Testing
- Test API connectivity for each provider
- Validate credentials against provider APIs
- Return detailed connection status and error messages
- Support for staging and production environments

### 4. Data Management
- Pagination support for configuration lists
- Search and filtering capabilities
- Audit trail with creation timestamps
- Soft delete functionality

## Technical Requirements

### Database Schema
- Table: user_provider_shipments
- Fields: id (uuid), user_id, name, key (encrypted), type (enum), created_at
- Enum: provider_shipment_type (GHN, GHTK, AHAMOVE, JT)
- Foreign key relationship with users table

### API Endpoints
- GET /api/v1/user/shipment-providers (list with pagination)
- GET /api/v1/user/shipment-providers/:id (get by ID)
- POST /api/v1/user/shipment-providers (create configuration)
- PUT /api/v1/user/shipment-providers/:id (update configuration)
- DELETE /api/v1/user/shipment-providers/:id (delete configuration)
- POST /api/v1/user/shipment-providers/test-connection (test connection)

### Provider-Specific Configurations

#### GHN (Giao Hàng Nhanh)
- Required: token (API Token), shopId (Shop ID)
- Optional: environment (staging/production)
- Test endpoint: GET /shiip/public-api/v2/shop/all

#### GHTK (Giao Hàng Tiết Kiệm)
- Required: apiToken (API Token), partnerCode (Partner Code)
- Optional: environment (staging/production)
- Test endpoint: GET /services/shipment/list_pick_add

#### Ahamove
- Required: apiKey (API Key), mobile (Phone number)
- Optional: token, refreshToken, tokenExpiry, environment
- Test endpoint: POST /v1/partner/authenticate

#### J&T Express
- Required: username, apiKey, secretKey (for MD5+Base64 signature)
- Optional: environment (staging/production)
- Test endpoint: API with signature authentication

### Security Requirements
- SHIPMENT_SECRET_KEY environment variable (32 characters)
- AES-256-GCM encryption with random IV
- Authentication tag for data integrity
- Sensitive data masking (show first 4 and last 4 characters)
- Input validation and sanitization

### Performance Requirements
- Response time < 500ms for CRUD operations
- Support for concurrent users
- Efficient database queries with proper indexing
- Pagination for large datasets

## Implementation Phases

### Phase 1: Foundation Setup
- Database migration creation and execution
- Environment configuration setup
- Basic entity and repository implementation
- Encryption service implementation

### Phase 2: Core API Development
- CRUD API endpoints implementation
- DTO validation and transformation
- Service layer business logic
- Controller layer with Swagger documentation

### Phase 3: Provider Integration
- Test connection implementations for all 4 providers
- Provider-specific validation logic
- Error handling and response formatting
- Connection status reporting

### Phase 4: Security & Testing
- Comprehensive security testing
- Input validation testing
- Encryption/decryption verification
- API endpoint testing
- Error scenario testing

### Phase 5: Documentation & Deployment
- API documentation completion
- Deployment preparation
- Performance optimization
- Monitoring and logging setup

## Success Criteria
- All CRUD operations functional and tested
- Test connection working for all 4 providers
- Data encryption/decryption working correctly
- Sensitive data properly masked in responses
- All TypeScript compilation and ESLint checks passing
- Comprehensive error handling implemented
- API documentation complete and accurate

## Risk Mitigation
- Provider API changes: Implement robust error handling and logging
- Security vulnerabilities: Regular security audits and updates
- Performance issues: Implement caching and query optimization
- Data corruption: Backup and recovery procedures

## Dependencies
- NestJS framework with TypeORM
- Crypto module for encryption
- Class-validator for input validation
- Swagger for API documentation
- Provider API documentation and access credentials

## Timeline
- Phase 1: 2 days (Database and foundation)
- Phase 2: 3 days (Core API development)
- Phase 3: 3 days (Provider integration)
- Phase 4: 2 days (Security and testing)
- Phase 5: 1 day (Documentation and deployment)
- Total: 11 days

## Acceptance Criteria
- All API endpoints return correct responses
- Encryption/decryption works without data loss
- Test connections succeed with valid credentials
- Invalid credentials are properly handled
- Sensitive data is never exposed in responses
- System handles concurrent users efficiently
- All security requirements are met
