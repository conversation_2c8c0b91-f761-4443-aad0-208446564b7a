import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOneOptions, Repository, SelectQueryBuilder } from 'typeorm';
import { UserSegment } from '../entities/user-segment.entity';

/**
 * Repository cho UserSegment
 */
@Injectable()
export class UserSegmentRepository {
  constructor(
    @InjectRepository(UserSegment)
    private readonly repository: Repository<UserSegment>,
  ) {}

  /**
   * Tìm kiếm nhiều segment
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách segment
   */
  async find(options?: FindManyOptions<UserSegment>): Promise<UserSegment[]> {
    return this.repository.find(options);
  }

  /**
   * Tìm kiếm một segment
   * @param options Tùy chọn tìm kiếm
   * @returns Segment hoặc null
   */
  async findOne(options?: FindOneOptions<UserSegment>): Promise<UserSegment | null> {
    if (!options) {
      return null;
    }
    return this.repository.findOne(options);
  }

  /**
   * Lưu segment
   * @param segment Segment cần lưu
   * @returns Segment đã lưu
   */
  async save(segment: UserSegment): Promise<UserSegment>;
  async save(segment: UserSegment[]): Promise<UserSegment[]>;
  async save(segment: UserSegment | UserSegment[]): Promise<UserSegment | UserSegment[]> {
    return this.repository.save(segment as any);
  }

  /**
   * Xóa segment
   * @param segment Segment cần xóa
   * @returns Segment đã xóa
   */
  async remove(segment: UserSegment): Promise<UserSegment>;
  async remove(segment: UserSegment[]): Promise<UserSegment[]>;
  async remove(segment: UserSegment | UserSegment[]): Promise<UserSegment | UserSegment[]> {
    return this.repository.remove(segment as any);
  }

  /**
   * Xóa nhiều segment theo ID
   * @param ids Danh sách ID của các segment
   * @param userId ID của người dùng (để kiểm tra quyền sở hữu)
   * @returns Kết quả xóa với danh sách thành công và thất bại
   */
  async removeMultiple(ids: number[], userId: number): Promise<{
    deletedIds: number[];
    failedIds: Array<{ id: number; reason: string }>;
  }> {
    const deletedIds: number[] = [];
    const failedIds: Array<{ id: number; reason: string }> = [];

    // Xử lý từng ID một cách tuần tự để có thể xử lý lỗi riêng biệt
    for (const id of ids) {
      try {
        // Tìm segment theo ID và userId
        const segment = await this.findOne({
          where: { id, userId },
        });

        if (!segment) {
          failedIds.push({ id, reason: 'Segment không tồn tại hoặc không có quyền truy cập' });
          continue;
        }

        // Cập nhật các campaign có tham chiếu đến segment này
        await this.executeQuery(
          `UPDATE user_campaigns SET segment_id = NULL WHERE segment_id = $1 AND user_id = $2`,
          [id, userId],
        );

        // Xóa segment
        await this.remove(segment);
        deletedIds.push(id);
      } catch (error) {
        let reason = 'Lỗi không xác định';
        if (error.message?.includes('không tồn tại')) {
          reason = 'Segment không tồn tại hoặc không có quyền truy cập';
        } else if (error.message) {
          reason = error.message;
        }
        failedIds.push({ id, reason });
      }
    }

    return { deletedIds, failedIds };
  }

  /**
   * Đếm số lượng segment
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng segment
   */
  async count(options?: FindManyOptions<UserSegment>): Promise<number> {
    return this.repository.countBy(options?.where || {});
  }

  /**
   * Tạo query builder
   * @param alias Alias cho entity
   * @returns Query builder
   */
  createQueryBuilder(alias?: string): SelectQueryBuilder<UserSegment> {
    return this.repository.createQueryBuilder(alias);
  }

  /**
   * Thực hiện truy vấn SQL trực tiếp
   * @param query Câu truy vấn SQL
   * @param parameters Tham số cho câu truy vấn
   * @returns Kết quả truy vấn
   */
  async executeQuery(query: string, parameters?: any[]): Promise<any> {
    return this.repository.query(query, parameters);
  }
}
