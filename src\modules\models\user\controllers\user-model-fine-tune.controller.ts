import { CurrentUser } from '@/modules/auth/decorators';
import { JwtUserGuard } from '@/modules/auth/guards';
import { QueryDto } from '@common/dto';
import { ApiResponseDto } from '@common/response';
import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUIDPipe,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { UserModelFineTuneService } from '../services/user-model-fine-tune.service';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý API cho User Model Fine Tune
 */
@ApiTags(SWAGGER_API_TAGS.USER_MODEL_FINE_TUNING)
@Controller('user/model-fine-tune')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserModelFineTuneController {
  constructor(private readonly userModelFineTuneService: UserModelFineTuneService) { }

  /**
   * Tạo model fine tune từ dataset và model base
   */
  @Post('create-from-dataset')
  @ApiOperation({
    summary: 'Tạo model fine tune từ dataset và model base',
    description: 'API này tạo model fine tune từ dataset người dùng và model base đã chọn'
  })
  @ApiResponse({
    status: 201,
    description: 'Tạo model fine tune thành công',
    type: ApiResponseDto
  })
  createFromDataset(
    @Body() createDto: any, // TODO: Tạo CreateModelFineTuneFromDatasetDto
    @CurrentUser('id') userId: number
  ) {
    return this.userModelFineTuneService.createFromDataset(userId, createDto);
  }

  /**
   * Lấy danh sách model fine tune của user có phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách model fine tune của user có phân trang',
    description: 'API này hỗ trợ tìm kiếm theo tên model, phân trang và sắp xếp'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách model fine tune',
    type: ApiResponseDto
  })
  findAll(
    @CurrentUser('id') userId: number,
    @Query() queryDto: QueryDto
  ) {
    return this.userModelFineTuneService.findAll(userId, queryDto);
  }

  /**
   * Lấy chi tiết model fine tune
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết model fine tune' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết model fine tune',
    type: ApiResponseDto
  })
  findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser('id') userId: number
  ) {
    return this.userModelFineTuneService.findOne(userId, id);
  }

  /**
   * Lấy lịch sử chi tiết của một fine-tune model
   */
  @Get(':id/history')
  @ApiOperation({
    summary: 'Lấy lịch sử chi tiết của một fine-tune model',
    description: 'API này trả về lịch sử chi tiết quá trình fine-tune của model'
  })
  @ApiResponse({
    status: 200,
    description: 'Lịch sử chi tiết fine-tune model',
    type: ApiResponseDto
  })
  getDetailedHistory(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser('id') userId: number
  ) {
    return this.userModelFineTuneService.getDetailedHistory(userId, id);
  }

  /**
   * Lấy trạng thái fine-tune
   */
  @Get(':id/status')
  @ApiOperation({
    summary: 'Lấy trạng thái fine-tune',
    description: 'API này trả về trạng thái hiện tại của quá trình fine-tune'
  })
  @ApiResponse({
    status: 200,
    description: 'Trạng thái fine-tune',
    type: ApiResponseDto
  })
  getFineTuneStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser('id') userId: number
  ) {
    return this.userModelFineTuneService.getFineTuneStatus(userId, id);
  }

  /**
   * Kiểm tra tính tương thích giữa model và dataset
   */
  @Post('check-compatibility')
  @ApiOperation({
    summary: 'Kiểm tra tính tương thích giữa model và dataset',
    description: 'API này kiểm tra xem dataset có tương thích với model base không'
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả kiểm tra tương thích',
    type: ApiResponseDto
  })
  checkCompatibility(
    @Body() compatibilityDto: any, // TODO: Tạo CheckCompatibilityDto
    @CurrentUser('id') userId: number
  ) {
    return this.userModelFineTuneService.checkCompatibility(userId, compatibilityDto);
  }

  /**
   * Hủy quá trình fine-tune
   */
  @Post(':id/cancel')
  @ApiOperation({
    summary: 'Hủy quá trình fine-tune',
    description: 'API này hủy quá trình fine-tune đang chạy'
  })
  @ApiResponse({
    status: 200,
    description: 'Hủy fine-tune thành công',
    type: ApiResponseDto
  })
  cancelFineTune(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser('id') userId: number
  ) {
    return this.userModelFineTuneService.cancelFineTune(userId, id);
  }

  /**
   * Test model fine tune
   */
  @Post(':id/test')
  @ApiOperation({
    summary: 'Test model fine tune',
    description: 'API này test model fine tune với input mẫu'
  })
  @ApiResponse({
    status: 200,
    description: 'Kết quả test model',
    type: ApiResponseDto
  })
  testModel(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() testDto: any, // TODO: Tạo TestModelDto
    @CurrentUser('id') userId: number
  ) {
    return this.userModelFineTuneService.testModel(userId, id, testDto);
  }
}
