-- Migration: Add audience_count column to user_segments table
-- Date: 2024-12-19
-- Description: Add audience_count column to track the number of audiences in each segment

-- Add the audience_count column
ALTER TABLE user_segments 
ADD COLUMN audience_count INTEGER DEFAULT 0;

-- Add comment for the new column
COMMENT ON COLUMN user_segments.audience_count IS 'Số lượng audience trong segment';

-- Grant permissions for the new column
GRANT SELECT, UPDATE ON user_segments TO member;

-- Optional: Update existing segments with calculated audience count
-- This can be run separately if needed
-- UPDATE user_segments SET audience_count = 0 WHERE audience_count IS NULL;
