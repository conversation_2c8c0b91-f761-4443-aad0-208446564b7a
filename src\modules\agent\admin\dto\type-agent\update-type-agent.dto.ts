import {ApiProperty, ApiPropertyOptional} from '@nestjs/swagger';
import {IsArray, IsBoolean, IsEnum, IsNumber, IsOptional, IsString, MaxLength, ValidateNested,} from 'class-validator';
import {Type} from 'class-transformer';
import {TypeAgentStatus} from '@modules/agent/constants';
import {TypeAgentConfig} from '@modules/agent/interfaces/type-agent-config.interface';

/**
 * DTO cho cập nhật cấu hình loại agent
 */
export class UpdateTypeAgentConfigDto implements Partial<TypeAgentConfig> {
  /**
     * <PERSON><PERSON> hồ sơ không
     */
    @ApiProperty({
      description: '<PERSON><PERSON> hồ sơ không',
      example: true,
    })
    @IsBoolean()
    enableAgentProfileCustomization: boolean;
  
    /**
     * <PERSON><PERSON> đầu ra không
     */
    @ApiProperty({
      description: '<PERSON><PERSON> đầu ra qua Messenger không',
      example: true,
    })
    @IsBoolean()
    enableOutputToMessenger: boolean;
  
    @ApiProperty({
      description: '<PERSON><PERSON> đầu ra qua Live Chat trên website không',
      example: true,
    })
    @IsBoolean()
    enableOutputToWebsiteLiveChat: boolean;
  
    /**
     * Có chuyển đổi không
     */
    @ApiProperty({
      description: 'Có chuyển đổi không',
      example: false,
    })
    @IsBoolean()
    enableTaskConversionTracking: boolean;
  
    /**
     * Có tài nguyên không
     */
    @ApiProperty({
      description: 'Có tài nguyên không',
      example: true,
    })
    @IsBoolean()
    enableResourceUsage: boolean;
  
    /**
     * Có chiến lược không
     */
    @ApiProperty({
      description: 'Có chiến lược không',
      example: true,
    })
    @IsBoolean()
    enableDynamicStrategyExecution: boolean;
  
    /**
     * Có đa agent không
     */
    @ApiProperty({
      description: 'Có đa agent không',
      example: true,
    })
    @IsBoolean()
    enableMultiAgentCollaboration: boolean;
}

/**
 * DTO cho việc cập nhật loại agent
 */
export class UpdateTypeAgentDto {
  /**
   * Tên loại agent
   */
  @ApiPropertyOptional({
    description: 'Tên loại agent',
    example: 'Chatbot Agent',
  })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  name?: string;

  /**
   * Mô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent hỗ trợ chat với người dùng',
  })
  @IsString()
  @IsOptional()
  description?: string;

  /**
   * Cấu hình mặc định cho loại agent
   */
  @ApiPropertyOptional({
    description: 'Cấu hình mặc định cho loại agent',
    example: {
      hasProfile: true,
      hasOutput: true,
      hasConversion: false,
      hasResources: true,
      hasStrategy: true,
      hasMultiAgent: false,
    },
  })
  @ValidateNested()
  @Type(() => UpdateTypeAgentConfigDto)
  @IsOptional()
  defaultConfig?: UpdateTypeAgentConfigDto;

  /**
   * Trạng thái của loại agent
   */
  @ApiPropertyOptional({
    description: 'Trạng thái của loại agent',
    enum: TypeAgentStatus,
  })
  @IsEnum(TypeAgentStatus)
  @IsOptional()
  status?: TypeAgentStatus;

  /**
   * Danh sách ID của các admin tools
   */
  @ApiPropertyOptional({
    description: 'Danh sách ID của các admin tools',
    example: ['uuid-1', 'uuid-2', 'uuid-3'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  toolIds?: string[];
}
