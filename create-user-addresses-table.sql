-- T<PERSON><PERSON> bảng user_addresses để quản lý địa chỉ của người dùng
CREATE TABLE user_addresses
(
    id               BIGSERIAL PRIMARY KEY,
    user_id          INTEGER                  NOT NULL
        REFERENCES users(id),
    recipient_name   VARCHAR(255)             NOT NULL,
    recipient_phone  VARCHAR(20)              NOT NULL,
    address          VARCHAR(500)             NOT NULL,
    province         VARCHAR(100),
    district         VARCHAR(100),
    ward             VARCHAR(100),
    postal_code      VARCHAR(10),
    is_default       BOOLEAN                  DEFAULT FALSE NOT NULL,
    address_type     VARCHAR(20)              DEFAULT 'home' NOT NULL,
    note             TEXT,
    is_active        BOOLEAN                  DEFAULT TRUE NOT NULL,
    created_at       BIGINT                   DEFAULT ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint NOT NULL,
    updated_at       BIGINT                   DEFAULT ((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint NOT NULL
);

-- Thêm comment cho bảng và các cột
COMMENT ON TABLE user_addresses IS 'Bảng quản lý địa chỉ của người dùng';

COMMENT ON COLUMN user_addresses.id IS 'ID địa chỉ';
COMMENT ON COLUMN user_addresses.user_id IS 'ID người dùng sở hữu địa chỉ';
COMMENT ON COLUMN user_addresses.recipient_name IS 'Tên người nhận';
COMMENT ON COLUMN user_addresses.recipient_phone IS 'Số điện thoại người nhận';
COMMENT ON COLUMN user_addresses.address IS 'Địa chỉ chi tiết';
COMMENT ON COLUMN user_addresses.province IS 'Tỉnh/Thành phố';
COMMENT ON COLUMN user_addresses.district IS 'Quận/Huyện';
COMMENT ON COLUMN user_addresses.ward IS 'Phường/Xã';
COMMENT ON COLUMN user_addresses.postal_code IS 'Mã bưu điện';
COMMENT ON COLUMN user_addresses.is_default IS 'Có phải địa chỉ mặc định không';
COMMENT ON COLUMN user_addresses.address_type IS 'Loại địa chỉ (home, office, other)';
COMMENT ON COLUMN user_addresses.note IS 'Ghi chú địa chỉ';
COMMENT ON COLUMN user_addresses.is_active IS 'Trạng thái hoạt động';
COMMENT ON COLUMN user_addresses.created_at IS 'Thời gian tạo địa chỉ';
COMMENT ON COLUMN user_addresses.updated_at IS 'Thời gian cập nhật địa chỉ';

-- Tạo index để tối ưu hóa truy vấn
CREATE INDEX idx_user_addresses_user_id ON user_addresses(user_id);
CREATE INDEX idx_user_addresses_user_id_default ON user_addresses(user_id, is_default);
CREATE INDEX idx_user_addresses_user_id_active ON user_addresses(user_id, is_active);

-- Thêm constraint để đảm bảo chỉ có một địa chỉ mặc định cho mỗi user
CREATE UNIQUE INDEX idx_user_addresses_unique_default 
ON user_addresses(user_id) 
WHERE is_default = true AND is_active = true;

-- Thêm constraint cho address_type
ALTER TABLE user_addresses 
ADD CONSTRAINT chk_address_type 
CHECK (address_type IN ('home', 'office', 'other'));

-- Phân quyền cho role member
GRANT SELECT, INSERT, UPDATE, DELETE ON user_addresses TO member;
GRANT SELECT, UPDATE, USAGE ON SEQUENCE user_addresses_id_seq TO member;

-- Thêm dữ liệu mẫu (tùy chọn)
INSERT INTO user_addresses (user_id, recipient_name, recipient_phone, address, province, district, ward, is_default, address_type, note) VALUES
(1, 'Nguyễn Văn A', '0912345678', '123 Đường ABC', 'TP. Hồ Chí Minh', 'Quận 1', 'Phường Bến Nghé', true, 'home', 'Nhà riêng'),
(1, 'Nguyễn Văn A', '0912345678', '456 Đường XYZ', 'TP. Hồ Chí Minh', 'Quận 3', 'Phường 1', false, 'office', 'Văn phòng công ty'),
(2, 'Trần Thị B', '0987654321', '789 Đường DEF', 'Hà Nội', 'Quận Ba Đình', 'Phường Điện Biên', true, 'home', 'Địa chỉ nhà');
