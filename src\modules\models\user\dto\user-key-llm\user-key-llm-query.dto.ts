import { ProviderEnum } from '@/modules/models/constants';
import { QueryDto, SortDirection } from '@common/dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';

/**
 * Enum cho các trường có thể sắp xếp
 */
export enum UserKeyLlmSortBy {
  NAME = 'name',
  PROVIDER = 'provider',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * DTO cho việc truy vấn danh sách user key LLM
 */
export class UserKeyLlmQueryDto extends QueryDto {
  /**
   * Lọc theo nhà cung cấp
   */
  @ApiPropertyOptional({
    description: 'Lọc theo nhà cung cấp',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
  })
  @IsOptional()
  @IsEnum(ProviderEnum)
  provider?: ProviderEnum;

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: UserKeyLlmSortBy,
    example: UserKeyLlmSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(UserKeyLlmSortBy)
  sortBy?: UserKeyLlmSortBy = UserKeyLlmSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
