import { AppException } from '@common/exceptions';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { MODELS_ERROR_CODES } from '../../exceptions';
import { ModelRegistryRepository } from '../../repositories/model-registry.repository';
import {
  CreateModelRegistryDto,
  ModelRegistryQueryDto,
  ModelRegistryResponseDto,
  UpdateModelRegistryDto
} from '../dto/model-registry';
import { ModelRegistryMapper } from '../mappers/model-registry.mapper';
import { ProviderEnum } from '../../constants';

/**
 * Service xử lý business logic cho Admin Model Registry
 */
@Injectable()
export class AdminModelRegistryService {
  private readonly logger = new Logger(AdminModelRegistryService.name);

  constructor(
    private readonly modelRegistryRepository: ModelRegistryRepository,
  ) { }

  // /**
  //  * Tạo mới model registry
  //  * @param createDto DTO tạo mới
  //  * @param employeeId ID của employee thực hiện
  //  * @returns Thông báo thành công
  //  */
  // @Transactional()
  // async create(createDto: CreateModelRegistryDto, employeeId: number): Promise<ApiResponseDto<{ message: string }>> {
  //   this.logger.log(`Creating model registry by employee ${employeeId}`);

  //   // Validate pattern
  //   if (!ModelRegistryMapper.validatePattern(createDto.modelNamePattern)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_REGISTRY_INVALID_PATTERN);
  //   }

  //   // Validate input modalities
  //   if (!ModelRegistryMapper.validateInputModalities(createDto.inputModalities)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   // Validate output modalities
  //   if (!ModelRegistryMapper.validateOutputModalities(createDto.outputModalities)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   // Validate sampling parameters
  //   if (createDto.samplingParameters && !ModelRegistryMapper.validateSamplingParameters(createDto.samplingParameters)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   // Validate features
  //   if (!ModelRegistryMapper.validateFeatures(createDto.features)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   // Kiểm tra trùng pattern
  //   const existsByPattern = await this.modelRegistryRepository.existsByPattern(createDto.modelNamePattern);
  //   if (existsByPattern) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_REGISTRY_PATTERN_EXISTS);
  //   }

  //   // Tạo entity mới
  //   const newRegistry = this.modelRegistryRepository.create({
  //     provider: createDto.provider || ProviderEnum.OPENAI, // Default provider
  //     modelNamePattern: createDto.modelNamePattern,
  //     inputModalities: ModelRegistryMapper.normalizeModalities(createDto.inputModalities) as any,
  //     outputModalities: ModelRegistryMapper.normalizeModalities(createDto.outputModalities) as any,
  //     samplingParameters: createDto.samplingParameters || {} as any,
  //     features: ModelRegistryMapper.normalizeFeatures(createDto.features) as any,
  //     createdBy: employeeId,
  //     updatedBy: employeeId
  //   });

  //   // Lưu vào database
  //   await this.modelRegistryRepository.save(newRegistry);

  //   this.logger.log(`Created model registry ${newRegistry.id} successfully`);
  //   return ApiResponseDto.success({ message: 'Tạo model registry thành công' });
  // }

  // /**
  //  * Lấy danh sách model registry có phân trang
  //  * @param queryDto Query parameters
  //  * @returns Danh sách model registry có phân trang
  //  */
  // async findAll(queryDto: ModelRegistryQueryDto): Promise<ApiResponseDto<PaginatedResult<ModelRegistryResponseDto>>> {
  //   this.logger.log('Getting model registry list');

  //   // Lấy dữ liệu từ repository
  //   const result = await this.modelRegistryRepository.findWithPagination(queryDto);

  //   // Lấy thông tin employee names (nếu cần)
  //   const employeeIds = new Set<number>();
  //   result.items.forEach(item => {
  //     if (item.createdBy) employeeIds.add(item.createdBy);
  //     if (item.updatedBy) employeeIds.add(item.updatedBy);
  //   });

  //   // TODO: Implement employee service to get names
  //   // const employeeNames = await this.getEmployeeNames(Array.from(employeeIds));

  //   // Convert sang DTO
  //   const items = ModelRegistryMapper.toResponseDtoArray(result.items);

  //   return ApiResponseDto.paginated({
  //     items,
  //     meta: result.meta
  //   });
  // }

  // /**
  //  * Lấy chi tiết model registry
  //  * @param id ID của model registry (UUID)
  //  * @returns Chi tiết model registry
  //  */
  // async findOne(id: string): Promise<ApiResponseDto<ModelRegistryResponseDto>> {
  //   this.logger.log(`Getting model registry detail: ${id}`);

  //   // Tìm model registry
  //   const registry = await this.modelRegistryRepository.createBaseQuery()
  //     .andWhere('modelRegistry.id = :id', { id })
  //     .getOne();

  //   if (!registry) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_REGISTRY_NOT_FOUND);
  //   }

  //   // TODO: Lấy thông tin employee names
  //   // const createdByName = registry.createdBy ? await this.getEmployeeName(registry.createdBy) : null;
  //   // const updatedByName = registry.updatedBy ? await this.getEmployeeName(registry.updatedBy) : null;

  //   // Convert sang DTO
  //   const responseDto = ModelRegistryMapper.toResponseDto(registry);

  //   return ApiResponseDto.success(responseDto);
  // }

  // /**
  //  * Cập nhật model registry
  //  * @param id ID của model registry (UUID)
  //  * @param updateDto DTO cập nhật
  //  * @param employeeId ID của employee thực hiện
  //  * @returns Thông báo thành công
  //  */
  // @Transactional()
  // async update(id: string, updateDto: UpdateModelRegistryDto, employeeId: number): Promise<ApiResponseDto<{ message: string }>> {
  //   this.logger.log(`Updating model registry ${id} by employee ${employeeId}`);

  //   // Tìm registry hiện tại
  //   const existingRegistry = await this.modelRegistryRepository.createBaseQuery()
  //     .andWhere('modelRegistry.id = :id', { id })
  //     .getOne();

  //   if (!existingRegistry) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_REGISTRY_NOT_FOUND);
  //   }

  //   // Validate pattern (nếu có thay đổi)
  //   if (updateDto.modelNamePattern && !ModelRegistryMapper.validatePattern(updateDto.modelNamePattern)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_REGISTRY_INVALID_PATTERN);
  //   }

  //   // Kiểm tra trùng pattern (nếu có thay đổi pattern)
  //   if (updateDto.modelNamePattern && updateDto.modelNamePattern !== existingRegistry.modelNamePattern) {
  //     const existsByPattern = await this.modelRegistryRepository.existsByPattern(updateDto.modelNamePattern, id);
  //     if (existsByPattern) {
  //       throw new AppException(MODELS_ERROR_CODES.MODEL_REGISTRY_PATTERN_EXISTS);
  //     }
  //   }

  //   // Validate các fields khác
  //   if (updateDto.inputModalities && !ModelRegistryMapper.validateInputModalities(updateDto.inputModalities)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   if (updateDto.outputModalities && !ModelRegistryMapper.validateOutputModalities(updateDto.outputModalities)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   if (updateDto.samplingParameters && !ModelRegistryMapper.validateSamplingParameters(updateDto.samplingParameters)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   if (updateDto.features && !ModelRegistryMapper.validateFeatures(updateDto.features)) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_VALIDATION_FAILED);
  //   }

  //   // Cập nhật các trường
  //   if (updateDto.provider !== undefined) {
  //     existingRegistry.provider = updateDto.provider;
  //   }
  //   if (updateDto.modelNamePattern !== undefined) {
  //     existingRegistry.modelNamePattern = updateDto.modelNamePattern;
  //   }
  //   if (updateDto.inputModalities !== undefined) {
  //     existingRegistry.inputModalities = ModelRegistryMapper.normalizeModalities(updateDto.inputModalities) as any;
  //   }
  //   if (updateDto.outputModalities !== undefined) {
  //     existingRegistry.outputModalities = ModelRegistryMapper.normalizeModalities(updateDto.outputModalities) as any;
  //   }
  //   if (updateDto.samplingParameters !== undefined) {
  //     existingRegistry.samplingParameters = updateDto.samplingParameters as any;
  //   }
  //   if (updateDto.features !== undefined) {
  //     existingRegistry.features = ModelRegistryMapper.normalizeFeatures(updateDto.features) as any;
  //   }

  //   existingRegistry.updatedBy = employeeId;
  //   existingRegistry.updatedAt = Date.now();

  //   // Lưu thay đổi
  //   await this.modelRegistryRepository.save(existingRegistry);

  //   this.logger.log(`Updated model registry ${id} successfully`);
  //   return ApiResponseDto.success({ message: 'Cập nhật model registry thành công' });
  // }

  // /**
  //  * Xóa model registry (soft delete)
  //  * @param id ID của model registry (UUID)
  //  * @param employeeId ID của employee thực hiện
  //  * @returns Thông báo thành công
  //  */
  // @Transactional()
  // async remove(id: string, employeeId: number): Promise<ApiResponseDto<{ message: string }>> {
  //   this.logger.log(`Soft deleting model registry ${id} by employee ${employeeId}`);

  //   // Kiểm tra registry tồn tại
  //   const existingRegistry = await this.modelRegistryRepository.createBaseQuery()
  //     .andWhere('modelRegistry.id = :id', { id })
  //     .getOne();

  //   if (!existingRegistry) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_REGISTRY_NOT_FOUND);
  //   }

  //   // Thực hiện soft delete
  //   const deleted = await this.modelRegistryRepository.softDeleteRegistry(id, employeeId);
  //   if (!deleted) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_REGISTRY_DELETE_FAILED);
  //   }

  //   this.logger.log(`Soft deleted model registry ${id} successfully`);
  //   return ApiResponseDto.success({ message: 'Xóa model registry thành công' });
  // }

  // /**
  //  * Lấy danh sách model registry đã xóa
  //  * @param queryDto Query parameters
  //  * @returns Danh sách model registry đã xóa
  //  */
  // async findDeleted(queryDto: ModelRegistryQueryDto): Promise<ApiResponseDto<PaginatedResult<ModelRegistryResponseDto>>> {
  //   this.logger.log('Getting deleted model registry list');

  //   // Lấy dữ liệu từ repository
  //   const result = await this.modelRegistryRepository.findDeletedWithPagination(queryDto);

  //   // Lấy thông tin employee names (nếu cần)
  //   const employeeIds = new Set<number>();
  //   result.items.forEach(item => {
  //     if (item.createdBy) employeeIds.add(item.createdBy);
  //     if (item.updatedBy) employeeIds.add(item.updatedBy);
  //     if (item.deletedBy) employeeIds.add(item.deletedBy);
  //   });

  //   // TODO: Implement employee service to get names
  //   // const employeeNames = await this.getEmployeeNames(Array.from(employeeIds));

  //   // Convert sang DTO
  //   const items = ModelRegistryMapper.toResponseDtoArray(result.items);

  //   return ApiResponseDto.paginated({
  //     items,
  //     meta: result.meta
  //   });
  // }

  // /**
  //  * Khôi phục model registry đã xóa
  //  * @param id ID của model registry cần khôi phục
  //  * @param employeeId ID của employee thực hiện
  //  * @returns Thông báo thành công
  //  */
  // @Transactional()
  // async restore(id: string, employeeId: number): Promise<ApiResponseDto<{ message: string }>> {
  //   this.logger.log(`Restoring model registry ${id} by employee ${employeeId}`);

  //   // Thực hiện khôi phục
  //   const restored = await this.modelRegistryRepository.restore(id);
  //   if (!restored) {
  //     throw new AppException(MODELS_ERROR_CODES.MODEL_REGISTRY_RESTORE_FAILED);
  //   }

  //   this.logger.log(`Restored model registry ${id} successfully`);
  //   return ApiResponseDto.success({ message: 'Khôi phục model registry thành công' });
  // }

  // /**
  //  * Test pattern matching với model name
  //  * @param pattern Pattern cần test
  //  * @param modelName Model name cần test
  //  * @returns Kết quả test
  //  */
  // async testPattern(pattern: string, modelName: string): Promise<ApiResponseDto<{ isMatch: boolean; pattern: string; modelName: string }>> {
  //   this.logger.log(`Testing pattern "${pattern}" with model name "${modelName}"`);

  //   const isMatch = ModelRegistryMapper.testPatternMatch(pattern, modelName);

  //   return ApiResponseDto.success({
  //     isMatch,
  //     pattern,
  //     modelName
  //   });
  // }
}
