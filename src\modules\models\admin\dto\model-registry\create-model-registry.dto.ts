import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength
} from 'class-validator';
import { ProviderEnum } from '@/modules/models/constants';

/**
 * DTO cho việc tạo mới model registry
 */
export class CreateModelRegistryDto {
  /**
   * Nhà cung cấp model
   */
  @ApiPropertyOptional({
    description: 'Nhà cung cấp model',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
    default: ProviderEnum.OPENAI,
  })
  @IsOptional()
  @IsEnum(ProviderEnum)
  provider?: ProviderEnum = ProviderEnum.OPENAI;

  /**
   * Tên mẫu đại diện của model
   */
  @ApiProperty({
    description: 'Tên mẫu đại diện của model',
    example: 'gpt-4*',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  modelNamePattern: string;

  /**
   * <PERSON><PERSON>c loại dữ liệu đầu vào hỗ trợ (text, image, audio,...)
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu vào hỗ trợ',
    example: ['text', 'image'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  inputModalities?: string[];

  /**
   * Các loại dữ liệu đầu ra hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu ra hỗ trợ',
    example: ['text'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  outputModalities?: string[];

  /**
   * Các tham số sampling như temperature, top_p,...
   */
  @ApiPropertyOptional({
    description: 'Các tham số sampling như temperature, top_p,...',
    example: [
      { name: 'temperature', type: 'number', min: 0, max: 2, default: 1 },
      { name: 'top_p', type: 'number', min: 0, max: 1, default: 1 }
    ],
    type: [Object],
  })
  @IsOptional()
  @IsArray()
  samplingParameters?: any[];

  /**
   * Tập hợp feature đặc biệt (như tool-use, function-calling)
   */
  @ApiPropertyOptional({
    description: 'Tập hợp feature đặc biệt',
    example: ['tool-use', 'function-calling', 'vision'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  features?: string[];
}
