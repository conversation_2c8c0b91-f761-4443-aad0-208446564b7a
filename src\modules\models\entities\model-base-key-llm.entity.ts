import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng model_base_key_llm trong cơ sở dữ liệu
 * Bảng ánh xạ nhiều-nhiều giữa model_base và system_key_llm
 */
@Entity('model_base_key_llm')
export class ModelBaseKeyLlm {
  /**
   * ID của model_base
   */
  @PrimaryColumn({ name: 'model_id', type: 'uuid' })
  modelId: string;

  /**
   * ID của system_key_llm
   */
  @PrimaryColumn({ name: 'system_key_llm_id', type: 'uuid' })
  systemKeyLlmId: string;

  /**
   * Có phải key mặc định cho model này không và chỉ có 1 key mặc định cho mỗi model
   */
  @Column({ name: 'is_default', type: 'boolean', default: false })
  isDefault: boolean;
}
