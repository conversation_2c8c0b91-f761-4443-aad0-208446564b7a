import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { ModelFineTune } from '../entities/model-fine-tune.entity';

/**
 * Repository cho ModelFineTune
 * X<PERSON> lý các thao tác CRUD và truy vấn nâng cao liên quan đến model fine-tune
 */
@Injectable()
export class ModelFineTuneRepository extends Repository<ModelFineTune> {
  private readonly logger = new Logger(ModelFineTuneRepository.name);

  constructor(private dataSource: DataSource) {
    super(ModelFineTune, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho ModelFineTune
   * @returns SelectQueryBuilder cho ModelFineTune
   */
  private createBaseQuery(): SelectQueryBuilder<ModelFineTune> {
    return this.createQueryBuilder('modelFineTune');
  }
}
