import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ProviderEnum } from '../constants/provider.enum';

/**
 * Entity đại diện cho bảng user_key_llm trong cơ sở dữ liệu
 * Lưu trữ các API Key LLM do người dùng tạo
 */
@Entity('user_key_llm')
export class UserKeyLlm {
  /**
   * UUID key người dùng
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên định danh cho key
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * Nhà cung cấp LLM
   */
  @Column({
    name: 'provider',
    type: 'enum',
    enum: ProviderEnum,
    default: ProviderEnum.OPENAI,
  })
  provider: ProviderEnum;

  /**
   * API key đã được mã hóa
   */
  @Column({
    type: 'text',
    name: 'api_key',
    comment: 'API key đã được mã hóa'
  })
  apiKey: string;

  /**
   * Người dùng sở hữu
   */
  @Column({
    name: 'user_id',
    type: 'integer',
    nullable: false,
    comment: 'ID của user sở hữu key'
  })
  userId: number;

  /**
   * Thời gian tạo
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Thời điểm xóa mềm
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;
}
