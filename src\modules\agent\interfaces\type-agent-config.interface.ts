export interface TypeAgentConfig {
  // --- <PERSON><PERSON><PERSON> tính năng cốt lõi của Agent ---
  /**
   * Cho phép tùy chỉnh và sử dụng hồ sơ (profile) cho AI Agent.
   * Ví dụ: Agent có thể có tên, avatar, hoặc vai trò cụ thể.
   */
  enableAgentProfileCustomization: boolean;

  /**
   * Cho phép theo dõi và phân tích việc hoàn thành nhiệm vụ hoặc mục tiêu của Agent.
   * Ví dụ: <PERSON> dõi tỷ lệ Agent giải quyết thành công yêu cầu của người dùng.
   */
  enableTaskConversionTracking: boolean;

  /**
   * Cho phép Agent thực thi các chiến l<PERSON> (strategies) động hoặc phức tạp đã được định nghĩa.
   * Ví dụ: Agent có thể thay đổi cách tiếp cận dựa trên ngữ cảnh hội thoại.
   */
  enableDynamicStrategyExecution: boolean;

  /**
   * Cho phép Agent hoạt động như một phần của hệ thống đa Agent, có khả năng phối hợp với các Agent khác.
   */
  enableMultiAgentCollaboration: boolean;

  // --- Các kênh đầu ra (Output Channels) của Agent ---

  // Nền tảng Messenger & Mạng xã hội
  /** Cho phép Agent tương tác qua Facebook Messenger. */
  enableOutputToMessenger: boolean;

  // Nền tảng Website
  /** Cho phép Agent tương tác qua Live Chat trên website. */
  enableOutputToWebsiteLiveChat: boolean;

  // --- Các tính năng bổ sung của Agent ---
  /** Cho phép Agent sử dụng và quản lý tài nguyên (resources) như dữ liệu, kiến thức, hoặc tài liệu. */
  enableResourceUsage: boolean;
}
