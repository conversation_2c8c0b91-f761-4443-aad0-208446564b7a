import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Logger,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { UserConvertCustomerService } from '@modules/business/user/services';
import { JwtUserGuard } from '@modules/auth/guards';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import {
  CreateUserConvertCustomerDto,
  CreateBulkUserConvertCustomerDto,
  BulkUserConvertCustomerResponseDto,
  UpdateUserConvertCustomerDto,
  MergeUserConvertCustomerDto,
  QueryUserConvertCustomerDto,
  UserConvertCustomerListItemDto,
  UserConvertCustomerResponseDto,
  UpdateCustomerSocialDto,
  CustomerSocialResponseDto,
  MetadataFieldDto,
  UpdateSocialLinksDto,
  SocialLinksResponseDto,
  BulkDeleteUserConvertCustomerDto,
  BulkDeleteUserConvertCustomerResponseDto,
  UpdateCustomerBasicInfoDto,
  CustomerBasicInfoResponseDto,
  UpdateCustomerCustomFieldsDto,
  CustomerCustomFieldsResponseDto,
  UpdateCustomerSocialLinksDto,
  CustomerSocialLinksResponseDto,
} from '../dto';
import { CurrentUser } from '@modules/auth/decorators';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { JwtPayload } from '@/modules/auth/guards/jwt.util';

/**
 * Controller xử lý các request liên quan đến khách hàng chuyển đổi
 */
@ApiTags(SWAGGER_API_TAGS.USER_CONVERT_CUSTOMER)
@Controller('user/convert-customers')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(
  ApiResponseDto,
  UserConvertCustomerResponseDto,
  UserConvertCustomerListItemDto,
  PaginatedResult,
  CreateUserConvertCustomerDto,
  CreateBulkUserConvertCustomerDto,
  BulkUserConvertCustomerResponseDto,
  MetadataFieldDto,
  UpdateUserConvertCustomerDto,
  MergeUserConvertCustomerDto,
  UpdateCustomerSocialDto,
  CustomerSocialResponseDto,
  UpdateSocialLinksDto,
  SocialLinksResponseDto,
  BulkDeleteUserConvertCustomerDto,
  BulkDeleteUserConvertCustomerResponseDto,
  UpdateCustomerBasicInfoDto,
  CustomerBasicInfoResponseDto,
  UpdateCustomerCustomFieldsDto,
  CustomerCustomFieldsResponseDto,
  UpdateCustomerSocialLinksDto,
  CustomerSocialLinksResponseDto,
)
export class UserConvertCustomerController {
  private readonly logger = new Logger(UserConvertCustomerController.name);

  constructor(
    private readonly userConvertCustomerService: UserConvertCustomerService,
  ) {}

  /**
   * Tạo khách hàng chuyển đổi mới
   * @param createDto DTO tạo khách hàng chuyển đổi
   * @param user Thông tin người dùng từ JWT
   * @returns Khách hàng chuyển đổi đã được tạo
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo khách hàng chuyển đổi mới',
    description: `Tạo khách hàng chuyển đổi mới với thông tin cơ bản và custom fields tùy chọn.

**Lưu ý quan trọng về Custom Fields:**
- Chỉ cần validate những custom fields được gửi lên trong metadata
- Không bắt buộc phải điền tất cả các trường required
- Có thể tạo khách hàng với chỉ 1 hoặc vài custom fields
- Custom fields có thể được tạo bởi user hoặc employee đều được chấp nhận
- Validation chỉ áp dụng cho những trường thực sự được gửi lên

**Cấu trúc metadata:**
- configId: ID cấu hình của custom field (bắt buộc)
- value: Giá trị của trường (bắt buộc, phải tuân theo validation rules của field)

**Ví dụ sử dụng:**
- Tạo với 1 custom field: chỉ cần gửi 1 item trong metadata
- Tạo với nhiều custom fields: gửi nhiều items trong metadata
- Tạo không có custom fields: bỏ qua metadata hoặc gửi mảng rỗng`,
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Khách hàng chuyển đổi đã được tạo thành công',
    schema: ApiResponseDto.getSchema(UserConvertCustomerResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CREATION_FAILED,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_PHONE_DUPLICATE,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_NOT_FOUND,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
  )
  async createConvertCustomer(
    @Body() createDto: CreateUserConvertCustomerDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserConvertCustomerResponseDto>> {
    try {
      this.logger.log(
        `Tạo khách hàng chuyển đổi mới cho userId=${user.id}, phone=${createDto.phone}`,
      );
      const customer = await this.userConvertCustomerService.create(
        user.id,
        createDto,
      );
      return ApiResponseDto.success(
        customer,
        'Tạo khách hàng chuyển đổi thành công',
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo khách hàng chuyển đổi: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Tạo nhiều khách hàng chuyển đổi cùng lúc
   * @param createBulkDto DTO tạo nhiều khách hàng chuyển đổi
   * @param user Thông tin người dùng từ JWT
   * @returns Kết quả bulk create
   */
  @Post('bulk')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Tạo nhiều khách hàng chuyển đổi cùng lúc',
    description: `Tạo nhiều khách hàng chuyển đổi trong một request. Hỗ trợ bỏ qua trùng lặp và tiếp tục khi có lỗi.

**Lưu ý về Custom Fields:**
- Áp dụng cùng logic như API tạo đơn lẻ
- Chỉ validate những custom fields được gửi lên trong metadata của mỗi khách hàng
- Không bắt buộc phải điền tất cả các trường required
- Mỗi khách hàng có thể có metadata khác nhau`,
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Bulk create hoàn thành thành công',
    schema: ApiResponseDto.getSchema(BulkUserConvertCustomerResponseDto),
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description:
      'Bulk create hoàn thành với một số lỗi hoặc không có khách hàng nào được tạo',
    schema: ApiResponseDto.getSchema(BulkUserConvertCustomerResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_BULK_CREATION_FAILED,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_BULK_VALIDATION_FAILED,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_PHONE_DUPLICATE,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CREATION_FAILED,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_BULK_PARTIAL_SUCCESS,
  )
  async createBulkConvertCustomers(
    @Body() createBulkDto: CreateBulkUserConvertCustomerDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<BulkUserConvertCustomerResponseDto>> {
    try {
      this.logger.log(
        `Tạo bulk ${createBulkDto.customers.length} khách hàng chuyển đổi cho userId=${user.id}`,
      );
      const result = await this.userConvertCustomerService.createBulk(
        user.id,
        createBulkDto,
      );

      // Xác định message dựa trên kết quả
      const hasErrors = result.errorCount > 0;
      const hasSuccess = result.successCount > 0;

      if (hasErrors && hasSuccess) {
        // Một số thành công, một số lỗi
        return ApiResponseDto.success(
          result,
          'Bulk create hoàn thành với một số lỗi',
        );
      } else if (hasSuccess) {
        // Tất cả thành công
        return ApiResponseDto.created(
          result,
          'Tạo bulk khách hàng chuyển đổi thành công',
        );
      } else {
        // Tất cả lỗi
        return ApiResponseDto.success(
          result,
          'Bulk create hoàn thành nhưng không có khách hàng nào được tạo',
        );
      }
    } catch (error) {
      this.logger.error(
        `Lỗi khi tạo bulk khách hàng chuyển đổi: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy danh sách khách hàng chuyển đổi của người dùng
   * @param queryDto DTO chứa các tham số truy vấn
   * @param user Thông tin người dùng từ JWT
   * @returns Danh sách khách hàng chuyển đổi với phân trang
   */
  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Lấy danh sách khách hàng chuyển đổi' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Danh sách khách hàng chuyển đổi',
    schema: ApiResponseDto.getPaginatedSchema(UserConvertCustomerListItemDto),
  })
  @ApiErrorResponse(BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_FIND_FAILED)
  async getConvertCustomers(
    @Query() queryDto: QueryUserConvertCustomerDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<PaginatedResult<UserConvertCustomerListItemDto>>> {
    try {
      this.logger.log(
        `Lấy danh sách khách hàng chuyển đổi cho userId=${user.id}`,
      );
      const customers = await this.userConvertCustomerService.findAll(
        user.id,
        queryDto,
      );
      return ApiResponseDto.success(
        customers,
        'Lấy danh sách khách hàng chuyển đổi thành công',
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách khách hàng chuyển đổi: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy chi tiết khách hàng chuyển đổi theo ID
   * @param id ID của khách hàng chuyển đổi
   * @param user Thông tin người dùng từ JWT
   * @returns Chi tiết khách hàng chuyển đổi
   */
  @Get('detail/:id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Lấy chi tiết khách hàng chuyển đổi' })
  @ApiParam({
    name: 'id',
    description: 'ID của khách hàng chuyển đổi',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Chi tiết khách hàng chuyển đổi',
    schema: ApiResponseDto.getSchema(UserConvertCustomerResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_NOT_FOUND,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_FIND_FAILED,
  )
  async getConvertCustomerDetail(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserConvertCustomerResponseDto>> {
    try {
      this.logger.log(
        `Lấy chi tiết khách hàng chuyển đổi id=${id} cho userId=${user.id}`,
      );
      const customer = await this.userConvertCustomerService.findById(
        id,
        user.id,
      );
      return ApiResponseDto.success(
        customer,
        'Lấy chi tiết khách hàng chuyển đổi thành công',
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy chi tiết khách hàng chuyển đổi: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật thông tin khách hàng chuyển đổi
   * @param id ID của khách hàng chuyển đổi
   * @param updateDto DTO cập nhật khách hàng chuyển đổi
   * @param user Thông tin người dùng từ JWT
   * @returns Khách hàng chuyển đổi đã được cập nhật
   */
  @Put(':id/information')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật thông tin khách hàng chuyển đổi',
    description:
      'Cập nhật thông tin cơ bản của khách hàng chuyển đổi như tên, số điện thoại, email, avatar, platform, timezone, agent.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của khách hàng chuyển đổi',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Khách hàng chuyển đổi đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(UserConvertCustomerResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_NOT_FOUND,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_PHONE_DUPLICATE,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CREATION_FAILED,
  )
  async updateConvertCustomerInformation(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateUserConvertCustomerDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserConvertCustomerResponseDto>> {
    try {
      this.logger.log(
        `Cập nhật thông tin khách hàng chuyển đổi id=${id} cho userId=${user.id}`,
      );
      const customer = await this.userConvertCustomerService.update(
        id,
        user.id,
        updateDto,
      );
      return ApiResponseDto.success(
        customer,
        'Cập nhật thông tin khách hàng chuyển đổi thành công',
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật thông tin khách hàng chuyển đổi: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Merge hai khách hàng chuyển đổi
   * Gộp 2 customer hiện có thành 1, customer nguồn sẽ bị xóa, customer đích sẽ được cập nhật
   * @param mergeDto DTO merge khách hàng chuyển đổi (chứa dữ liệu cuối cùng sau merge)
   * @param user Thông tin người dùng từ JWT
   * @returns Khách hàng chuyển đổi đã được merge
   */
  @Put('merge')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Merge hai khách hàng chuyển đổi',
    description:
      'Gộp 2 customer hiện có thành 1. Customer nguồn sẽ bị xóa, customer đích sẽ được cập nhật với dữ liệu merge.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Khách hàng chuyển đổi đã được merge thành công',
    schema: ApiResponseDto.getSchema(UserConvertCustomerResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_NOT_FOUND,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_MERGE_SAME_CUSTOMER,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_MERGE_DIFFERENT_USER,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_PHONE_DUPLICATE,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_MERGE_FAILED,
  )
  async mergeConvertCustomers(
    @Body() mergeDto: MergeUserConvertCustomerDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<UserConvertCustomerResponseDto>> {
    try {
      this.logger.log(
        `Merge khách hàng chuyển đổi cho userId=${user.id}, source=${mergeDto.sourceCustomerId}, target=${mergeDto.targetCustomerId}`,
      );
      const mergedCustomer = await this.userConvertCustomerService.merge(
        user.id,
        mergeDto,
      );
      return ApiResponseDto.success(
        mergedCustomer,
        'Merge khách hàng chuyển đổi thành công',
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi merge khách hàng chuyển đổi: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật thông tin social cho khách hàng chuyển đổi
   * @param id ID của khách hàng chuyển đổi
   * @param updateDto DTO cập nhật thông tin social
   * @param user Thông tin người dùng từ JWT
   * @returns Thông tin social đã cập nhật
   */
  @Put(':id/social')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật thông tin social cho khách hàng chuyển đổi',
    description:
      'Cập nhật thông tin Facebook và Web cho khách hàng chuyển đổi. Thao tác này sẽ thay thế toàn bộ thông tin social hiện tại.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của khách hàng chuyển đổi',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thông tin social đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(CustomerSocialResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_NOT_FOUND,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.CUSTOMER_SOCIAL_UPDATE_FAILED,
    BUSINESS_ERROR_CODES.CUSTOMER_SOCIAL_VALIDATION_FAILED,
    BUSINESS_ERROR_CODES.CUSTOMER_FACEBOOK_PAGE_SCOPED_ID_DUPLICATE,
  )
  async updateSocial(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateCustomerSocialDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<CustomerSocialResponseDto>> {
    try {
      this.logger.log(
        `Cập nhật thông tin social cho khách hàng ${id} của userId=${user.id}`,
      );
      const result = await this.userConvertCustomerService.updateSocial(
        id,
        user.id,
        updateDto,
      );
      return ApiResponseDto.success(
        result,
        'Cập nhật thông tin social thành công',
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật thông tin social: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật thông tin cơ bản của khách hàng chuyển đổi
   * @param id ID của khách hàng chuyển đổi
   * @param updateDto DTO cập nhật thông tin cơ bản
   * @param user Thông tin người dùng từ JWT
   * @returns Thông tin cơ bản đã được cập nhật
   */
  @Put(':id/basic-info')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật thông tin cơ bản của khách hàng chuyển đổi',
    description: 'Cập nhật thông tin cơ bản như tên, số điện thoại, email, địa chỉ của khách hàng chuyển đổi.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của khách hàng chuyển đổi',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Thông tin cơ bản đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(CustomerBasicInfoResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_NOT_FOUND,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_PHONE_DUPLICATE,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CREATION_FAILED,
  )
  async updateBasicInfo(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateCustomerBasicInfoDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<CustomerBasicInfoResponseDto>> {
    try {
      this.logger.log(
        `Cập nhật thông tin cơ bản khách hàng chuyển đổi id=${id} cho userId=${user.id}`,
      );
      const result = await this.userConvertCustomerService.updateBasicInfo(
        id,
        user.id,
        updateDto,
      );
      return ApiResponseDto.success(
        result,
        'Cập nhật thông tin cơ bản khách hàng chuyển đổi thành công',
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật thông tin cơ bản khách hàng chuyển đổi: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật trường tùy chỉnh của khách hàng chuyển đổi
   * @param id ID của khách hàng chuyển đổi
   * @param updateDto DTO cập nhật trường tùy chỉnh
   * @param user Thông tin người dùng từ JWT
   * @returns Trường tùy chỉnh đã được cập nhật
   */
  @Put(':id/custom-fields')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật trường tùy chỉnh của khách hàng chuyển đổi',
    description: 'Cập nhật các trường tùy chỉnh (metadata) của khách hàng chuyển đổi.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của khách hàng chuyển đổi',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Trường tùy chỉnh đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(CustomerCustomFieldsResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_NOT_FOUND,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_NOT_FOUND,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CUSTOM_FIELD_VALIDATION_FAILED,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CREATION_FAILED,
  )
  async updateCustomFields(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateCustomerCustomFieldsDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<CustomerCustomFieldsResponseDto>> {
    try {
      this.logger.log(
        `Cập nhật trường tùy chỉnh khách hàng chuyển đổi id=${id} cho userId=${user.id}`,
      );
      const result = await this.userConvertCustomerService.updateCustomFields(
        id,
        user.id,
        updateDto,
      );
      return ApiResponseDto.success(
        result,
        'Cập nhật trường tùy chỉnh khách hàng chuyển đổi thành công',
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật trường tùy chỉnh khách hàng chuyển đổi: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật liên kết mạng xã hội của khách hàng chuyển đổi
   * @param id ID của khách hàng chuyển đổi
   * @param updateDto DTO cập nhật liên kết mạng xã hội
   * @param user Thông tin người dùng từ JWT
   * @returns Liên kết mạng xã hội đã được cập nhật
   */
  @Put(':id/social-links')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Cập nhật liên kết mạng xã hội của khách hàng chuyển đổi',
    description: 'Cập nhật các liên kết mạng xã hội như Facebook, Twitter, LinkedIn, Zalo, Website của khách hàng chuyển đổi.',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của khách hàng chuyển đổi',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Liên kết mạng xã hội đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(CustomerSocialLinksResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_NOT_FOUND,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_ACCESS_DENIED,
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_CREATION_FAILED,
  )
  async updateSocialLinks(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateCustomerSocialLinksDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<CustomerSocialLinksResponseDto>> {
    try {
      this.logger.log(
        `Cập nhật liên kết mạng xã hội khách hàng chuyển đổi id=${id} cho userId=${user.id}`,
      );
      const result = await this.userConvertCustomerService.updateSocialLinks(
        id,
        user.id,
        updateDto,
      );
      return ApiResponseDto.success(
        result,
        'Cập nhật liên kết mạng xã hội khách hàng chuyển đổi thành công',
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật liên kết mạng xã hội khách hàng chuyển đổi: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xóa nhiều khách hàng chuyển đổi (hard delete)
   * @param bulkDeleteDto DTO chứa danh sách ID khách hàng chuyển đổi cần xóa
   * @param user Thông tin người dùng từ JWT
   * @returns Kết quả xóa nhiều khách hàng chuyển đổi
   */
  @Delete('bulk')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Xóa nhiều khách hàng chuyển đổi' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa nhiều khách hàng chuyển đổi thành công',
    schema: ApiResponseDto.getSchema(BulkDeleteUserConvertCustomerResponseDto),
  })
  @ApiResponse({
    status: 207,
    description: 'Một số khách hàng chuyển đổi không thể xóa',
    schema: ApiResponseDto.getSchema(BulkDeleteUserConvertCustomerResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_NOT_FOUND,
    BUSINESS_ERROR_CODES.USER_CONVERT_CUSTOMER_DELETE_FAILED,
  )
  async bulkDeleteConvertCustomers(
    @Body() bulkDeleteDto: BulkDeleteUserConvertCustomerDto,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<BulkDeleteUserConvertCustomerResponseDto>> {
    try {
      this.logger.log(
        `Xóa bulk ${bulkDeleteDto.customerIds.length} khách hàng chuyển đổi cho userId=${user.id}`,
      );
      const result = await this.userConvertCustomerService.bulkDeleteCustomers(
        bulkDeleteDto,
        user.id,
      );
      return ApiResponseDto.success(result, result.message);
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa bulk khách hàng chuyển đổi: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xóa khách hàng chuyển đổi (hard delete)
   * @param id ID của khách hàng chuyển đổi cần xóa
   * @param user Thông tin người dùng từ JWT
   * @returns Thông báo xóa thành công
   */
  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Xóa khách hàng chuyển đổi' })
  @ApiParam({
    name: 'id',
    description: 'ID của khách hàng chuyển đổi',
    type: Number,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Khách hàng chuyển đổi đã được xóa thành công',
  })
  @ApiErrorResponse(
    BUSINESS_ERROR_CODES.CONVERT_CUSTOMER_NOT_FOUND,
    BUSINESS_ERROR_CODES.USER_CONVERT_CUSTOMER_DELETE_FAILED,
  )
  async deleteConvertCustomer(
    @Param('id', ParseIntPipe) id: number,
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<null>> {
    try {
      this.logger.log(
        `Xóa khách hàng chuyển đổi id=${id} cho userId=${user.id}`,
      );
      await this.userConvertCustomerService.deleteCustomer(id, user.id);
      return ApiResponseDto.success(null, 'Xóa khách hàng chuyển đổi thành công');
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa khách hàng chuyển đổi: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
