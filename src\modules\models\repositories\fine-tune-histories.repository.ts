import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { FineTuneHistories } from '../entities/fine-tune-histories.entity';

/**
 * Repository cho FineTuneHistories
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến lịch sử fine-tune
 */
@Injectable()
export class FineTuneHistoriesRepository extends Repository<FineTuneHistories> {
  private readonly logger = new Logger(FineTuneHistoriesRepository.name);

  constructor(private dataSource: DataSource) {
    super(FineTuneHistories, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho FineTuneHistories
   * @returns SelectQueryBuilder cho FineTuneHistories
   */
  private createBaseQuery(): SelectQueryBuilder<FineTuneHistories> {
    return this.createQueryBuilder('fineTuneHistories');
  }
}
