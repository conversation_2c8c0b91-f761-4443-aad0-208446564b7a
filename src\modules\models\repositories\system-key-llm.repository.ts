import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { SystemKeyLlm } from '../entities/system-key-llm.entity';
import { PaginatedResult } from '@common/response';
import { SystemKeyLlmQueryDto } from '../admin/dto/system-key-llm';
import { ProviderEnum } from '../constants/provider.enum';

/**
 * Repository cho SystemKeyLlm
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến system key LLM
 */
@Injectable()
export class SystemKeyLlmRepository extends Repository<SystemKeyLlm> {
  private readonly logger = new Logger(SystemKeyLlmRepository.name);

  constructor(private dataSource: DataSource) {
    super(SystemKeyLlm, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho SystemKeyLlm
   * @returns SelectQueryBuilder cho SystemKeyLlm
   */
  createBaseQuery(): SelectQueryBuilder<SystemKeyLlm> {
    return this.createQueryBuilder('systemKeyLlm')
      .select([
        'systemKeyLlm.id',
        'systemKeyLlm.name',
        'systemKeyLlm.provider',
        'systemKeyLlm.baseUrl',
        'systemKeyLlm.description',
        'systemKeyLlm.status',
        'systemKeyLlm.isDefault',
        'systemKeyLlm.rateLimitRpm',
        'systemKeyLlm.rateLimitTpm',
        'systemKeyLlm.expiresAt',
        'systemKeyLlm.metadata',
        'systemKeyLlm.createdAt',
        'systemKeyLlm.updatedAt',
        'systemKeyLlm.createdBy',
        'systemKeyLlm.updatedBy'
      ])
      .where('systemKeyLlm.deletedAt IS NULL');
  }

  /**
   * Tìm kiếm system key LLM với phân trang
   * @param queryDto DTO query
   * @returns Kết quả phân trang
   */
  async findWithPagination(queryDto: SystemKeyLlmQueryDto): Promise<PaginatedResult<SystemKeyLlm>> {
    const query = this.createBaseQuery();
    
    query.select(['systemKeyLlm.id', 'systemKeyLlm.name', 'systemKeyLlm.provider']);

    // Tìm kiếm theo tên
    if (queryDto.search) {
      query.andWhere('systemKeyLlm.name ILIKE :search', {
        search: `%${queryDto.search}%`
      });
    }

    // Lọc theo provider
    if (queryDto.provider) {
      query.andWhere('systemKeyLlm.provider = :provider', {
        provider: queryDto.provider
      });
    }

    // Sắp xếp
    if (queryDto.sortBy) {
      const direction = queryDto.sortDirection || 'ASC';
      query.orderBy(`systemKeyLlm.${queryDto.sortBy}`, direction);
    } else {
      query.orderBy('systemKeyLlm.createdAt', 'DESC');
    }

    // Phân trang
    const skip = (queryDto.page - 1) * queryDto.limit;
    query.skip(skip).take(queryDto.limit);

    const [items, totalItems] = await query.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: queryDto.limit,
        totalPages: Math.ceil(totalItems / queryDto.limit),
        currentPage: queryDto.page
      }
    };
  }

  /**
   * Tìm system key LLM theo ID với đầy đủ dữ liệu (bao gồm encrypted API key)
   * @param id ID của system key
   * @returns SystemKeyLlm hoặc null
   */
  async findByIdWithFullData(id: string): Promise<SystemKeyLlm | null> {
    return this.createQueryBuilder('systemKeyLlm')
      .select([
        'systemKeyLlm.id',
        'systemKeyLlm.name',
        'systemKeyLlm.provider',
        'systemKeyLlm.apiKey', // Include encrypted API key
      ])
      .where('systemKeyLlm.id = :id', { id })
      .andWhere('systemKeyLlm.deletedAt IS NULL')
      .getOne();
  }

  /**
   * Kiểm tra tồn tại theo tên (để tránh trùng lặp)
   * @param name Tên system key
   * @param excludeId ID cần loại trừ (cho trường hợp update)
   * @returns true nếu tồn tại
   */
  async existsByName(name: string, excludeId?: string): Promise<boolean> {
    const query = this.createQueryBuilder('systemKeyLlm')
      .where('systemKeyLlm.name = :name', { name })
      .andWhere('systemKeyLlm.deletedAt IS NULL');

    if (excludeId) {
      query.andWhere('systemKeyLlm.id != :excludeId', { excludeId });
    }

    const count = await query.getCount();
    return count > 0;
  }

  /**
   * Tìm key mặc định theo provider
   * @param provider Nhà cung cấp
   * @returns SystemKeyLlm hoặc null
   */
  async findDefaultByProvider(provider: ProviderEnum): Promise<SystemKeyLlm | null> {
    return this.createBaseQuery()
      .andWhere('systemKeyLlm.provider = :provider', { provider })
      .andWhere('systemKeyLlm.isDefault = :isDefault', { isDefault: true })
      .getOne();
  }

  /**
   * Tìm tất cả keys theo provider
   * @param provider Nhà cung cấp
   * @returns Mảng SystemKeyLlm
   */
  async findByProvider(provider: ProviderEnum): Promise<SystemKeyLlm[]> {
    return this.createBaseQuery()
      .andWhere('systemKeyLlm.provider = :provider', { provider })
      .orderBy('systemKeyLlm.isDefault', 'DESC')
      .addOrderBy('systemKeyLlm.createdAt', 'ASC')
      .getMany();
  }

  /**
   * Tìm keys sắp hết hạn (trong 7 ngày)
   * @returns Mảng SystemKeyLlm
   */
  async findExpiringSoon(): Promise<SystemKeyLlm[]> {
    const now = Date.now();
    const sevenDaysFromNow = now + (7 * 24 * 60 * 60 * 1000);

    return this.createBaseQuery()
      .andWhere('systemKeyLlm.expiresAt IS NOT NULL')
      .andWhere('systemKeyLlm.expiresAt > :now', { now })
      .andWhere('systemKeyLlm.expiresAt <= :sevenDaysFromNow', { sevenDaysFromNow })
      .orderBy('systemKeyLlm.expiresAt', 'ASC')
      .getMany();
  }

  /**
   * Tìm keys đã hết hạn
   * @returns Mảng SystemKeyLlm
   */
  async findExpired(): Promise<SystemKeyLlm[]> {
    const now = Date.now();

    return this.createBaseQuery()
      .andWhere('systemKeyLlm.expiresAt IS NOT NULL')
      .andWhere('systemKeyLlm.expiresAt < :now', { now })
      .orderBy('systemKeyLlm.expiresAt', 'DESC')
      .getMany();
  }

  /**
   * Tìm các system keys đã xóa với phân trang
   * @param queryDto DTO query
   * @returns Kết quả phân trang
   */
  async findDeletedWithPagination(queryDto: SystemKeyLlmQueryDto): Promise<PaginatedResult<SystemKeyLlm>> {
    const query = this.createQueryBuilder('systemKeyLlm')
      .select([
        'systemKeyLlm.id',
        'systemKeyLlm.name',
        'systemKeyLlm.provider',
      ])
      .where('systemKeyLlm.deletedAt IS NOT NULL');

    // Tìm kiếm theo tên
    if (queryDto.search) {
      query.andWhere('systemKeyLlm.name ILIKE :search', {
        search: `%${queryDto.search}%`
      });
    }

    // Lọc theo provider
    if (queryDto.provider) {
      query.andWhere('systemKeyLlm.provider = :provider', {
        provider: queryDto.provider
      });
    }

    // Sắp xếp theo thời gian xóa
    query.orderBy('systemKeyLlm.deletedAt', 'DESC');

    // Phân trang
    const skip = (queryDto.page - 1) * queryDto.limit;
    query.skip(skip).take(queryDto.limit);

    const [items, totalItems] = await query.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: queryDto.limit,
        totalPages: Math.ceil(totalItems / queryDto.limit),
        currentPage: queryDto.page
      }
    };
  }

  /**
   * Soft delete system key
   * @param id ID của system key
   * @param deletedBy ID người xóa
   * @returns true nếu thành công
   */
  async softDeleteSystemKey(id: string, deletedBy: number): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .update(SystemKeyLlm)
      .set({
        deletedAt: Date.now(),
        deletedBy
      })
      .where('id = :id', { id })
      .andWhere('deletedAt IS NULL')
      .execute();

    return (result.affected || 0) > 0;
  }

  /**
   * Khôi phục system key đã xóa
   * @param id ID của system key
   * @returns true nếu thành công
   */
  async restoreCustom(id: string[]): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .update(SystemKeyLlm)
      .set({
        deletedAt: null,
        deletedBy: null
      })
      .where('id IN :id', { id })
      .andWhere('deletedAt IS NOT NULL')
      .execute();

    return (result.affected || 0) > 0;
  }

  /**
   * Kiểm tra và khôi phục system keys với kết quả chi tiết
   * @param ids Mảng ID cần khôi phục
   * @returns Object chứa danh sách restored và failed IDs
   */
  async restoreWithDetails(ids: string[]): Promise<{ restored: string[]; failed: string[] }> {
    if (!ids || ids.length === 0) {
      return { restored: [], failed: [] };
    }

    // Tìm các IDs có thể khôi phục (đã bị xóa)
    const deletedKeys = await this.createQueryBuilder('systemKeyLlm')
      .select(['systemKeyLlm.id'])
      .where('systemKeyLlm.id IN (:...ids)', { ids })
      .andWhere('systemKeyLlm.deletedAt IS NOT NULL')
      .getMany();

    const restorableIds = deletedKeys.map(key => key.id);
    const failedIds = ids.filter(id => !restorableIds.includes(id));

    // Thực hiện khôi phục các IDs hợp lệ
    if (restorableIds.length > 0) {
      await this.createQueryBuilder()
        .update(SystemKeyLlm)
        .set({
          deletedAt: null,
          deletedBy: null
        })
        .where('id IN (:...ids)', { ids: restorableIds })
        .execute();
    }

    return {
      restored: restorableIds,
      failed: failedIds
    };
  }

  /**
   * Cập nhật key mặc định cho provider
   * Chỉ có thể có 1 key mặc định cho mỗi provider
   * @param id ID của key sẽ trở thành mặc định
   * @param provider Provider
   * @returns true nếu thành công
   */
  async setAsDefault(id: string, provider: ProviderEnum): Promise<boolean> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Bỏ default của tất cả keys khác trong provider
      await queryRunner.manager
        .createQueryBuilder()
        .update(SystemKeyLlm)
        .set({ isDefault: false })
        .where('provider = :provider', { provider })
        .andWhere('deletedAt IS NULL')
        .execute();

      // Set key hiện tại làm default
      const result = await queryRunner.manager
        .createQueryBuilder()
        .update(SystemKeyLlm)
        .set({ isDefault: true })
        .where('id = :id', { id })
        .andWhere('deletedAt IS NULL')
        .execute();

      await queryRunner.commitTransaction();
      return (result.affected || 0) > 0;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}