import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto } from '@common/dto';
import { SortDirection } from '@common/dto';

/**
 * Enum cho các trường có thể sắp xếp
 */
export enum ModelRegistrySortBy {
  MODEL_NAME_PATTERN = 'modelNamePattern',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * DTO cho việc truy vấn danh sách model registry
 */
export class ModelRegistryQueryDto extends QueryDto {
  /**
   * Tìm kiếm theo tên pattern
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên pattern',
    example: 'gpt',
  })
  @IsOptional()
  @IsString()
  modelNamePattern?: string;

  /**
   * Tìm kiếm theo feature
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo feature',
    example: 'tool-use',
  })
  @IsOptional()
  @IsString()
  feature?: string;

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: ModelRegistrySortBy,
    example: ModelRegistrySortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(ModelRegistrySortBy)
  sortBy?: ModelRegistrySortBy = ModelRegistrySortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
