# Test API Tạo Đơn Hàng <PERSON>i

## Endpoint
```
POST /v1/user/orders
```

## Headers
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

## Request Body Example

### Tạo đơn hàng với khách hàng mới
```json
{
  "customerInfo": {
    "fullName": "Nguyễn Văn A",
    "phone": "0912345678",
    "email": "<EMAIL>",
    "address": "123 Đường ABC, Quận 1, TP.HCM"
  },
  "products": [
    {
      "productId": 1,
      "quantity": 2
    },
    {
      "productId": 2,
      "quantity": 1
    }
  ],
  "billInfo": {
    "subtotal": 300000,
    "tax": 30000,
    "shippingFee": 20000,
    "discount": 10000,
    "total": 340000,
    "paymentMethod": "COD",
    "paymentStatus": "PENDING",
    "codAmount": 340000
  },
  "hasShipping": true,
  "logisticInfo": {
    "shippingMethod": "Gia<PERSON> hàng nhanh",
    "carrier": "GHN",
    "shippingNote": "Giao hàng trong giờ hành chính",
    "deliveryAddress": "456 Đường XYZ, Quận 2, TP.HCM"
  },
  "shippingStatus": "PENDING",
  "orderStatus": "PENDING",
  "source": "website",
  "note": "Đơn hàng ưu tiên",
  "tags": ["urgent", "vip"]
}
```

### Tạo đơn hàng với khách hàng đã có
```json
{
  "customerInfo": {
    "customerId": 101,
    "fullName": "Nguyễn Văn B",
    "phone": "**********",
    "email": "<EMAIL>",
    "address": "789 Đường DEF, Quận 3, TP.HCM"
  },
  "products": [
    {
      "productId": 3,
      "quantity": 1
    }
  ],
  "billInfo": {
    "subtotal": 150000,
    "total": 150000,
    "paymentMethod": "BANKING",
    "paymentStatus": "PAID"
  },
  "hasShipping": false,
  "orderStatus": "CONFIRMED",
  "source": "mobile_app"
}
```

## Response Example

### Success Response (201 Created)
```json
{
  "success": true,
  "message": "Tạo đơn hàng thành công",
  "data": {
    "id": 123,
    "userConvertCustomerId": 101,
    "userId": 1001,
    "productInfo": [
      {
        "productId": 1,
        "name": "Áo thun nam",
        "quantity": 2,
        "unitPrice": 150000,
        "totalPrice": 300000,
        "description": "Áo thun nam chất liệu cotton",
        "images": ["image1.jpg", "image2.jpg"]
      }
    ],
    "billInfo": {
      "subtotal": 300000,
      "tax": 30000,
      "shippingFee": 20000,
      "discount": 10000,
      "total": 340000,
      "paymentMethod": "COD",
      "paymentStatus": "PENDING",
      "codAmount": 340000
    },
    "hasShipping": true,
    "shippingStatus": "PENDING",
    "logisticInfo": {
      "shippingMethod": "Giao hàng nhanh",
      "carrier": "GHN",
      "shippingNote": "Giao hàng trong giờ hành chính",
      "deliveryAddress": "456 Đường XYZ, Quận 2, TP.HCM",
      "note": "Đơn hàng ưu tiên",
      "tags": ["urgent", "vip"]
    },
    "createdAt": 1741708800000,
    "updatedAt": 1741708800000,
    "source": "website",
    "orderStatus": "PENDING"
  }
}
```

### Error Responses

#### 400 Bad Request - Validation Error
```json
{
  "success": false,
  "message": "Dữ liệu đầu vào không hợp lệ",
  "error": {
    "code": 400,
    "details": [
      {
        "field": "customerInfo.fullName",
        "message": "Họ và tên không được để trống"
      },
      {
        "field": "products",
        "message": "Danh sách sản phẩm không được để trống"
      }
    ]
  }
}
```

#### 404 Not Found - Product Not Found
```json
{
  "success": false,
  "message": "Sản phẩm với ID 999 không tồn tại hoặc không thuộc về bạn",
  "error": {
    "code": 30001,
    "message": "Product not found"
  }
}
```

#### 404 Not Found - Customer Not Found
```json
{
  "success": false,
  "message": "Khách hàng không tồn tại hoặc không thuộc về bạn",
  "error": {
    "code": 30181,
    "message": "Khách hàng không tồn tại"
  }
}
```

#### 500 Internal Server Error - Order Creation Failed
```json
{
  "success": false,
  "message": "Lỗi khi tạo đơn hàng: Database connection failed",
  "error": {
    "code": 30022,
    "message": "Lỗi khi tạo đơn hàng"
  }
}
```

## Enums

### PaymentMethodEnum
- `COD` - Thanh toán khi nhận hàng
- `BANKING` - Chuyển khoản ngân hàng
- `CREDIT_CARD` - Thẻ tín dụng
- `DEBIT_CARD` - Thẻ ghi nợ
- `E_WALLET` - Ví điện tử
- `QR_CODE` - QR Code
- `CASH` - Tiền mặt
- `OTHER` - Khác

### PaymentStatusEnum
- `PENDING` - Chờ thanh toán
- `PAID` - Đã thanh toán
- `FAILED` - Thanh toán thất bại
- `REFUNDED` - Đã hoàn tiền
- `PARTIAL_PAID` - Thanh toán một phần
- `CANCELLED` - Đã hủy

### ShippingStatusEnum
- `PENDING` - Chờ xử lý
- `PREPARING` - Đang chuẩn bị
- `SHIPPED` - Đã giao cho đơn vị vận chuyển
- `IN_TRANSIT` - Đang vận chuyển/giao hàng
- `SORTING` - Đang phân loại
- `DELIVERED` - Đã giao thành công
- `DELIVERY_FAILED` - Giao hàng thất bại
- `RETURNING` - Đang trả lại
- `CANCELLED` - Đã hủy

### OrderStatusEnum
- `PENDING` - Đang chờ xử lý
- `CONFIRMED` - Đã xác nhận
- `PROCESSING` - Đang xử lý
- `COMPLETED` - Đã hoàn thành
- `CANCELLED` - Đã hủy

## Notes

1. **Khách hàng mới**: Nếu không cung cấp `customerId`, hệ thống sẽ tự động tạo khách hàng mới
2. **Khách hàng đã có**: Nếu cung cấp `customerId`, hệ thống sẽ kiểm tra khách hàng có tồn tại và thuộc về user không
3. **Sản phẩm**: Tất cả sản phẩm trong đơn hàng phải thuộc về user hiện tại
4. **Giá sản phẩm**: Hệ thống sẽ tự động lấy giá từ thông tin sản phẩm (salePrice hoặc listPrice)
5. **Địa chỉ giao hàng**: Nếu không cung cấp `deliveryAddress` trong `logisticInfo`, sẽ sử dụng địa chỉ khách hàng
6. **Trạng thái mặc định**: 
   - `orderStatus`: `PENDING`
   - `shippingStatus`: `PENDING`
   - `paymentStatus`: `PENDING`
   - `hasShipping`: `true`
   - `source`: `website`
