import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, MaxLength } from 'class-validator';

/**
 * DTO cho việc cập nhật system key LLM
 */
export class UpdateSystemKeyLlmDto {
  @ApiPropertyOptional({
    description: 'Tên định danh cho key',
    example: 'OpenAI Production Key v2',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiPropertyOptional({
    description: 'API Key truy cập LLM (để cập nhật key)',
    example: 'sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
  })
  @IsOptional()
  @IsString()
  apiKey?: string;
}
