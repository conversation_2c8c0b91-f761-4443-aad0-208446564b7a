import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';
import { ProviderEnumq } from '@/shared/services/ai/utils/type-provider.util';

/**
 * DTO cho response của model info chi tiết
 */
@Exclude()
export class ModelInfoResponseDto {
  /**
   * Model ID từ nhà cung cấp
   */
  @ApiProperty({
    description: 'Model ID từ nhà cung cấp',
    example: 'gpt-4-turbo-preview',
  })
  @Expose()
  modelId: string;

  /**
   * Tên hiển thị của model
   */
  @ApiProperty({
    description: 'Tên hiển thị của model',
    example: 'GPT-4 Turbo Preview',
  })
  @Expose()
  name: string;

  /**
   * Nhà cung cấp model
   */
  @ApiProperty({
    description: 'Nhà cung cấp model',
    enum: ProviderEnumq,
    example: ProviderEnumq.OPENAI,
  })
  @Expose()
  provider: ProviderEnumq;

  /**
   * <PERSON><PERSON> tả chi tiết về model
   */
  @ApiPropertyOptional({
    description: '<PERSON>ô tả chi tiết về model',
    example: 'GPT-4 Turbo with improved instruction following, JSON mode, and reproducible outputs',
  })
  @Expose()
  description?: string;

  /**
   * Version của model
   */
  @ApiPropertyOptional({
    description: 'Version của model',
    example: '2024-01-25',
  })
  @Expose()
  version?: string;

  /**
   * Chi phí input per 1k tokens (USD)
   */
  @ApiPropertyOptional({
    description: 'Chi phí input per 1k tokens (USD)',
    example: 0.01,
  })
  @Expose()
  inputCostPer1kTokens?: number;

  /**
   * Chi phí output per 1k tokens (USD)
   */
  @ApiPropertyOptional({
    description: 'Chi phí output per 1k tokens (USD)',
    example: 0.03,
  })
  @Expose()
  outputCostPer1kTokens?: number;

  /**
   * Độ dài context tối đa
   */
  @ApiPropertyOptional({
    description: 'Độ dài context tối đa',
    example: 128000,
  })
  @Expose()
  contextLength?: number;

  /**
   * Max output tokens
   */
  @ApiPropertyOptional({
    description: 'Max output tokens',
    example: 4096,
  })
  @Expose()
  maxOutputTokens?: number;

  /**
   * Capabilities của model
   */
  @ApiPropertyOptional({
    description: 'Capabilities của model',
    example: ['text-generation', 'function-calling', 'json-mode'],
  })
  @Expose()
  capabilities?: string[];

  /**
   * Supported languages
   */
  @ApiPropertyOptional({
    description: 'Supported languages',
    example: ['en', 'vi', 'zh', 'ja', 'ko', 'fr', 'de', 'es'],
  })
  @Expose()
  supportedLanguages?: string[];

  /**
   * Training data cutoff
   */
  @ApiPropertyOptional({
    description: 'Training data cutoff',
    example: '2023-04',
  })
  @Expose()
  trainingDataCutoff?: string;

  /**
   * Model parameters (nếu có thông tin)
   */
  @ApiPropertyOptional({
    description: 'Model parameters (nếu có thông tin)',
    example: '175B',
  })
  @Expose()
  parameters?: string;

  /**
   * Architecture type
   */
  @ApiPropertyOptional({
    description: 'Architecture type',
    example: 'transformer',
  })
  @Expose()
  architecture?: string;

  /**
   * Availability status
   */
  @ApiProperty({
    description: 'Availability status',
    example: {
      isAvailable: true,
      lastChecked: 1640995200000,
      responseTime: 250,
      status: 'operational'
    },
  })
  @Expose()
  availability: {
    isAvailable: boolean;
    lastChecked: number;
    responseTime?: number;
    status: 'operational' | 'degraded' | 'down' | 'maintenance';
  };

  /**
   * Rate limits (nếu có)
   */
  @ApiPropertyOptional({
    description: 'Rate limits (nếu có)',
    example: {
      requestsPerMinute: 3500,
      tokensPerMinute: 90000,
      requestsPerDay: 10000
    },
  })
  @Expose()
  rateLimits?: {
    requestsPerMinute?: number;
    tokensPerMinute?: number;
    requestsPerDay?: number;
  };

  /**
   * Source của model info
   */
  @ApiProperty({
    description: 'Source của model info',
    enum: ['admin', 'user-key', 'registry'],
    example: 'admin',
  })
  @Expose()
  source: 'admin' | 'user-key' | 'registry';

  /**
   * Thông tin user key (nếu source là user-key)
   */
  @ApiPropertyOptional({
    description: 'Thông tin user key (nếu source là user-key)',
    example: {
      keyId: '123e4567-e89b-12d3-a456-426614174000',
      keyName: 'My OpenAI Key',
      lastTested: 1640995200000
    },
  })
  @Expose()
  userKeyInfo?: {
    keyId: string;
    keyName: string;
    lastTested: number;
  };

  /**
   * Usage recommendations
   */
  @ApiPropertyOptional({
    description: 'Usage recommendations',
    example: {
      bestFor: ['chat', 'code-generation', 'analysis'],
      notRecommendedFor: ['real-time', 'streaming'],
      tips: ['Use system messages for better results', 'Enable JSON mode for structured output']
    },
  })
  @Expose()
  recommendations?: {
    bestFor: string[];
    notRecommendedFor: string[];
    tips: string[];
  };

  /**
   * Metadata bổ sung
   */
  @ApiPropertyOptional({
    description: 'Metadata bổ sung',
    example: {
      multimodal: false,
      streaming: true,
      functionCalling: true
    },
  })
  @Expose()
  metadata?: any;
}
