import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng fine_tune_histories trong cơ sở dữ liệu
 * Lưu thông tin chi tiết về mỗi lần fine-tune mô hình, bao gồm metadata, phương pháp và thời gian
 */
@Entity('fine_tune_histories')
export class FineTuneHistories {
  /**
   * ID duy nhất của mỗi bản ghi fine-tune
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên model được fine-tune
   */
  @Column({ name: 'model_name', type: 'varchar', length: 255, nullable: false })
  modelName: string;

  /**
   * Số token sử dụng trong quá trình fine-tune
   */
  @Column({ name: 'token', type: 'bigint', default: 0 })
  token: number;

  /**
   * Phương pháp fine-tune được sử dụng (ví dụ: LoRA, QLoRA, Full)
   */
  @Column({ name: 'method', type: 'jsonb', nullable: true })
  method: any;

  /**
   * Thông tin cấu hình như learning rate, số epoch, dữ liệu sử dụng...
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: any;

  /**
   * ID người dùng đã khởi tạo quá trình fine-tune
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  /**
   * Thời gian bắt đầu fine-tune (tính bằng millisecond kể từ epoch)
   */
  @Column({
    name: 'start_date',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  startDate: number;

  /**
   * Thời gian kết thúc fine-tune (tính bằng millisecond kể từ epoch)
   */
  @Column({
    name: 'end_date',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  endDate: number;
}
