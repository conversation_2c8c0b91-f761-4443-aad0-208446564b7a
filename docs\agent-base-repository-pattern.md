# Agent Base Repository Pattern Documentation

## Tổng quan

BaseAgentRepository là abstract class cung cấp foundation cho tất cả repositories trong Agent Module. Class này implement các pattern và best practices để đảm bảo consistency và maintainability.

## Kiến trúc

### Class Structure

```typescript
export abstract class BaseAgentRepository<T extends ObjectLiteral> extends Repository<T>
```

- **Generic Type**: `T extends ObjectLiteral` để type safety
- **Inheritance**: Extends TypeORM `Repository<T>`
- **Abstract**: Không thể instantiate trực tiếp

### Dependencies

- **SqlHelper**: Pagination và query optimization
- **Logger**: Debugging và monitoring
- **DataSource**: TypeORM database connection

## Core Methods

### 1. createBaseQuery() - Abstract Method

```typescript
protected abstract createBaseQuery(): SelectQueryBuilder<T>
```

**<PERSON><PERSON><PERSON> đích**: Template method pattern - mỗi repository phải implement
**Usage**: Foundation cho tất cả queries

**Example Implementation**:
```typescript
protected createBaseQuery(): SelectQueryBuilder<Agent> {
  return this.createQueryBuilder('agent')
    .select([
      'agent.id',
      'agent.name',
      'agent.avatar',
      'agent.createdAt',
      'agent.updatedAt'
    ]);
}
```

### 2. Soft Delete Methods

#### softDelete(id, deletedBy?)
```typescript
async softDelete(id: string, deletedBy?: number): Promise<void>
```

**Mục đích**: Soft delete entity thay vì hard delete
**Parameters**:
- `id`: Entity ID
- `deletedBy`: User ID thực hiện xóa (optional)

**Implementation**:
- Set `deletedAt = Date.now()`
- Set `deletedBy = userId` (nếu có)
- Log action

#### restore(id)
```typescript
async restore(id: string): Promise<void>
```

**Mục đích**: Khôi phục entity đã bị soft delete
**Implementation**:
- Set `deletedAt = null`
- Set `deletedBy = null`

#### findDeleted(query)
```typescript
async findDeleted(query: QueryDto): Promise<PaginatedResult<T>>
```

**Mục đích**: Lấy danh sách entities đã bị soft delete
**Usage**: Admin management, audit trail

### 3. Query Methods

#### findById(id, withSoftDelete?)
```typescript
async findById(id: string, withSoftDelete: boolean = true): Promise<T | null>
```

**Features**:
- Automatic soft delete filtering (default: true)
- Type-safe return
- Null handling

#### existsById(id, withSoftDelete?)
```typescript
async existsById(id: string, withSoftDelete: boolean = true): Promise<boolean>
```

**Mục đích**: Performance-optimized existence check
**Usage**: Validation, business logic

#### findPaginated(query, options?)
```typescript
async findPaginated(
  query: QueryDto,
  options?: PaginationOptions
): Promise<PaginatedResult<T>>
```

**Features**:
- SqlHelper integration
- Automatic soft delete filtering
- Custom query builder support
- Search và sort support

**Options**:
```typescript
interface PaginationOptions {
  alias?: string;
  selectFields?: (keyof T)[];
  searchFields?: (keyof T)[];
  customize?: (qb: SelectQueryBuilder<T>) => SelectQueryBuilder<T>;
  withSoftDelete?: boolean;
}
```

### 4. Utility Methods

#### count(withSoftDelete?)
```typescript
async count(withSoftDelete: boolean = true): Promise<number>
```

#### findAll(withSoftDelete?)
```typescript
async findAll(withSoftDelete: boolean = true): Promise<T[]>
```

#### updateTimestamp(id, updatedBy?)
```typescript
protected async updateTimestamp(id: string, updatedBy?: number): Promise<void>
```

#### createWithTimestamp(entityData, createdBy?)
```typescript
protected async createWithTimestamp(entityData: Partial<T>, createdBy?: number): Promise<T>
```

## Implementation Guide

### Step 1: Create Entity Repository

```typescript
@Injectable()
export class AgentRepository extends BaseAgentRepository<Agent> {
  constructor(dataSource: DataSource) {
    super(Agent, dataSource, 'AgentRepository');
  }

  protected createBaseQuery(): SelectQueryBuilder<Agent> {
    return this.createQueryBuilder('agent')
      .select([
        'agent.id',
        'agent.name',
        'agent.avatar',
        'agent.modelConfig',
        'agent.instruction',
        'agent.createdAt',
        'agent.updatedAt',
        'agent.status'
      ]);
  }

  // Custom methods specific to Agent
  async findByStatus(status: string): Promise<Agent[]> {
    return this.createBaseQueryWithSoftDelete()
      .andWhere('agent.status = :status', { status })
      .getMany();
  }
}
```

### Step 2: Register in Module

```typescript
@Module({
  imports: [TypeOrmModule.forFeature([Agent])],
  providers: [AgentRepository],
  exports: [AgentRepository],
})
export class AgentModule {}
```

### Step 3: Use in Service

```typescript
@Injectable()
export class AgentService {
  constructor(private agentRepository: AgentRepository) {}

  async getAgents(query: QueryDto): Promise<PaginatedResult<Agent>> {
    return this.agentRepository.findPaginated(query, {
      searchFields: ['name', 'instruction'],
      customize: (qb) => {
        return qb.andWhere('agent.status = :status', { status: 'APPROVED' });
      }
    });
  }

  async deleteAgent(id: string, deletedBy: number): Promise<void> {
    await this.agentRepository.softDelete(id, deletedBy);
  }
}
```

## Best Practices

### 1. Query Optimization

```typescript
// ✅ GOOD: Select specific fields
protected createBaseQuery(): SelectQueryBuilder<Agent> {
  return this.createQueryBuilder('agent')
    .select(['agent.id', 'agent.name', 'agent.status']);
}

// ❌ BAD: Select all fields
protected createBaseQuery(): SelectQueryBuilder<Agent> {
  return this.createQueryBuilder('agent'); // Selects all fields
}
```

### 2. Soft Delete Consistency

```typescript
// ✅ GOOD: Always use soft delete
await this.agentRepository.softDelete(id, userId);

// ❌ BAD: Hard delete
await this.agentRepository.delete(id);
```

### 3. Error Handling

```typescript
// ✅ GOOD: Check existence before operations
const exists = await this.agentRepository.existsById(id);
if (!exists) {
  throw new AppException(ErrorCode.AGENT_NOT_FOUND);
}

// ❌ BAD: Assume entity exists
const agent = await this.agentRepository.findById(id);
agent.name = newName; // Potential null reference error
```

### 4. Pagination Usage

```typescript
// ✅ GOOD: Use findPaginated with options
const result = await this.agentRepository.findPaginated(query, {
  searchFields: ['name'],
  customize: (qb) => qb.andWhere('agent.status = :status', { status: 'ACTIVE' })
});

// ❌ BAD: Manual pagination
const agents = await this.agentRepository.findAll();
const paginatedAgents = agents.slice((page - 1) * limit, page * limit);
```

## Advanced Features

### Custom Query Builder

```typescript
async findAgentsWithDetails(query: QueryDto): Promise<PaginatedResult<Agent>> {
  return this.findPaginated(query, {
    customize: (qb) => {
      return qb
        .leftJoinAndSelect('agent.user', 'user')
        .leftJoinAndSelect('agent.typeAgent', 'typeAgent')
        .andWhere('user.active = :active', { active: true });
    }
  });
}
```

### Complex Filtering

```typescript
async findAgentsByMultipleCriteria(
  status: string,
  userId?: number,
  query?: QueryDto
): Promise<PaginatedResult<Agent>> {
  return this.findPaginated(query || new QueryDto(), {
    customize: (qb) => {
      qb.andWhere('agent.status = :status', { status });
      
      if (userId) {
        qb.andWhere('agent.userId = :userId', { userId });
      }
      
      return qb;
    }
  });
}
```

## Migration Guide

### From Existing Repository

```typescript
// OLD: Direct TypeORM usage
@Injectable()
export class OldAgentRepository extends Repository<Agent> {
  async findAgents(): Promise<Agent[]> {
    return this.find({ where: { deletedAt: IsNull() } });
  }
}

// NEW: BaseAgentRepository usage
@Injectable()
export class AgentRepository extends BaseAgentRepository<Agent> {
  protected createBaseQuery(): SelectQueryBuilder<Agent> {
    return this.createQueryBuilder('agent');
  }
  
  async findAgents(): Promise<Agent[]> {
    return this.findAll(); // Automatic soft delete filtering
  }
}
```

## Performance Considerations

1. **Index Usage**: Ensure `deletedAt` field is indexed
2. **Select Fields**: Always specify required fields in createBaseQuery()
3. **Pagination**: Use findPaginated() instead of findAll() for large datasets
4. **Caching**: Consider implementing query result caching for frequently accessed data

## Testing

```typescript
describe('AgentRepository', () => {
  let repository: AgentRepository;

  beforeEach(async () => {
    // Setup test module
  });

  it('should soft delete agent', async () => {
    const agent = await repository.createWithTimestamp({ name: 'Test Agent' });
    await repository.softDelete(agent.id, 1);
    
    const deletedAgent = await repository.findById(agent.id, false);
    expect(deletedAgent.deletedAt).toBeDefined();
  });

  it('should restore soft deleted agent', async () => {
    const agent = await repository.createWithTimestamp({ name: 'Test Agent' });
    await repository.softDelete(agent.id, 1);
    await repository.restore(agent.id);
    
    const restoredAgent = await repository.findById(agent.id);
    expect(restoredAgent).toBeDefined();
    expect(restoredAgent.deletedAt).toBeNull();
  });
});
```
