import { UserKeyLlm } from '../../entities/user-key-llm.entity';
import { UserKeyLlmResponseDto, TestConnectionResponseDto } from '../dto/user-key-llm';

/**
 * Mapper cho UserKeyLlm
 * Chuyển đổi giữa entity và DTO
 */
export class UserKeyLlmMapper {
  /**
   * Chuyển đổi entity sang response DTO
   * @param entity UserKeyLlm entity
   * @returns UserKeyLlmResponseDto
   */
  static toResponseDto(entity: UserKeyLlm): UserKeyLlmResponseDto {
    return {
      id: entity.id,
      name: entity.name,
      provider: entity.provider,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  /**
   * Chuyển đổi mảng entities sang mảng response DTOs
   * @param entities Mảng UserKeyLlm entities
   * @returns Mảng UserKeyLlmResponseDto
   */
  static toResponseDtoArray(entities: UserKeyLlm[]): UserKeyLlmResponseDto[] {
    return entities.map(entity => this.toResponseDto(entity));
  }

  /**
   * Mask API key để hiển thị an toàn
   * @param encryptedApiKey Encrypted API key
   * @returns Masked API key
   */
  static maskApiKey(encryptedApiKey?: string): string {
    if (!encryptedApiKey) {
      return '****';
    }

    // Giả sử API key có format như sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
    // Hiển thị 4 ký tự đầu và 4 ký tự cuối
    if (encryptedApiKey.length <= 8) {
      return '****';
    }

    const start = encryptedApiKey.substring(0, 4);
    const end = encryptedApiKey.substring(encryptedApiKey.length - 4);
    const middle = '*'.repeat(Math.max(4, encryptedApiKey.length - 8));

    return `${start}${middle}${end}`;
  }

  /**
   * Validate key name
   * @param name Tên key cần validate
   * @returns true nếu hợp lệ
   */
  static validateKeyName(name: string): boolean {
    if (!name || name.trim().length === 0) {
      return false;
    }

    // Tên key không được chứa ký tự đặc biệt nguy hiểm
    const dangerousChars = /[<>\"'&]/;
    if (dangerousChars.test(name)) {
      return false;
    }

    // Tên key phải có ít nhất 2 ký tự và không quá 255 ký tự
    const trimmedName = name.trim();
    if (trimmedName.length < 2 || trimmedName.length > 255) {
      return false;
    }

    return true;
  }

  /**
   * Tạo response DTO cho test connection thành công
   * @param responseTime Thời gian phản hồi
   * @param providerInfo Thông tin provider
   * @param sampleModels Sample models
   * @param quotaInfo Quota info
   * @returns TestConnectionResponseDto
   */
  static toTestConnectionSuccessDto(
    responseTime: number,
  ): TestConnectionResponseDto {
    return {
      success: true,
      responseTime,
    };
  }

  /**
   * Tạo response DTO cho test connection thất bại
   * @param error Thông báo lỗi
   * @returns TestConnectionResponseDto
   */
  static toTestConnectionFailureDto(error: string): TestConnectionResponseDto {
    return {
      success: false,
      error
    };
  }

  /**
   * Normalize provider name
   * @param provider Provider name
   * @returns Normalized provider name
   */
  static normalizeProvider(provider: string): string {
    return provider.toUpperCase().trim();
  }

  /**
   * Get default base URL cho provider
   * @param provider Provider
   * @returns Default base URL
   */
  static getDefaultBaseUrl(provider: string): string | undefined {
    switch (provider.toUpperCase()) {
      case 'OPENAI':
        return 'https://api.openai.com/v1';
      case 'ANTHROPIC':
        return 'https://api.anthropic.com';
      case 'GOOGLE':
        return 'https://generativelanguage.googleapis.com/v1';
      case 'XAI':
        return 'https://api.x.ai/v1';
      case 'DEEPSEEK':
        return 'https://api.deepseek.com/v1';
      case 'META':
        return 'https://api.llama-api.com/v1';
      default:
        return undefined;
    }
  }

  /**
   * Check if key is expired based on metadata
   * @param metadata Key metadata
   * @returns true nếu key đã hết hạn
   */
  static isKeyExpired(metadata?: any): boolean {
    if (!metadata || !metadata.expiresAt) {
      return false;
    }

    const expiresAt = typeof metadata.expiresAt === 'number'
      ? metadata.expiresAt
      : Date.parse(metadata.expiresAt);

    return Date.now() > expiresAt;
  }

  /**
   * Check if key is expiring soon (within 7 days)
   * @param metadata Key metadata
   * @returns true nếu key sắp hết hạn
   */
  static isKeyExpiringSoon(metadata?: any): boolean {
    if (!metadata || !metadata.expiresAt) {
      return false;
    }

    const expiresAt = typeof metadata.expiresAt === 'number'
      ? metadata.expiresAt
      : Date.parse(metadata.expiresAt);

    const sevenDaysFromNow = Date.now() + (7 * 24 * 60 * 60 * 1000);
    return expiresAt <= sevenDaysFromNow && expiresAt > Date.now();
  }

  /**
   * Format last test result for display
   * @param lastTestResult Last test result
   * @returns Formatted string
   */
  static formatLastTestResult(lastTestResult?: any): string {
    if (!lastTestResult) {
      return 'Chưa test';
    }

    const { success, testedAt, responseTime, error } = lastTestResult;
    const testDate = new Date(testedAt).toLocaleString('vi-VN');

    if (success) {
      const timeStr = responseTime ? ` (${responseTime}ms)` : '';
      return `✅ Thành công${timeStr} - ${testDate}`;
    } else {
      return `❌ Thất bại: ${error || 'Unknown error'} - ${testDate}`;
    }
  }
}
