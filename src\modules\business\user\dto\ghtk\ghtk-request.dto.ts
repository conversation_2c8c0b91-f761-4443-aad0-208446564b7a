import { IsString, IsOptional, IsN<PERSON>ber, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { GHTKProductDto, GHTKOrderDto } from './ghtk-config.dto';

/**
 * DTO cho request tạo đơn hàng GHTK
 */
export class CreateGHTKOrderRequestDto {
  @ApiProperty({
    description: 'Danh sách sản phẩm trong đơn hàng',
    type: [GHTKProductDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GHTKProductDto)
  products: GHTKProductDto[];

  @ApiProperty({
    description: 'Thông tin đơn hàng',
    type: GHTKOrderDto
  })
  @ValidateNested()
  @Type(() => GHTKOrderDto)
  order: GHTKOrderDto;
}

/**
 * DTO cho request tính phí vận chuyển GHTK
 */
export class CalculateGHTKFeeRequestDto {
  @ApiProperty({
    description: 'Mã địa chỉ lấy hàng (nếu có)',
    example: '88256',
    required: false
  })
  @IsOptional()
  @IsString()
  pickAddressId?: string;

  @ApiProperty({
    description: 'Địa chỉ lấy hàng',
    example: 'nhà số 5, tổ 3, ngách 11, ngõ 45',
    required: false
  })
  @IsOptional()
  @IsString()
  pickAddress?: string;

  @ApiProperty({
    description: 'Tỉnh/thành nơi lấy hàng',
    example: 'Hà Nội'
  })
  @IsString()
  pickProvince: string;

  @ApiProperty({
    description: 'Quận/huyện lấy hàng',
    example: 'Quận Hai Bà Trưng'
  })
  @IsString()
  pickDistrict: string;

  @ApiProperty({
    description: 'Phường/xã lấy hàng',
    example: 'Phường Bách Khoa',
    required: false
  })
  @IsOptional()
  @IsString()
  pickWard?: string;

  @ApiProperty({
    description: 'Đường/phố lấy hàng',
    example: 'Đại Cồ Việt',
    required: false
  })
  @IsOptional()
  @IsString()
  pickStreet?: string;

  @ApiProperty({
    description: 'Địa chỉ chi tiết người nhận',
    example: 'Chung cư CT1, ngõ 58, Trần Bình',
    required: false
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({
    description: 'Tỉnh/thành người nhận hàng',
    example: 'Hà Nội'
  })
  @IsString()
  province: string;

  @ApiProperty({
    description: 'Quận/huyện người nhận hàng',
    example: 'Quận Cầu Giấy'
  })
  @IsString()
  district: string;

  @ApiProperty({
    description: 'Phường/xã người nhận hàng',
    example: 'Phường Dịch Vọng',
    required: false
  })
  @IsOptional()
  @IsString()
  ward?: string;

  @ApiProperty({
    description: 'Đường/phố người nhận',
    example: 'Xuân Thủy',
    required: false
  })
  @IsOptional()
  @IsString()
  street?: string;

  @ApiProperty({
    description: 'Tổng cân nặng gói hàng (gram)',
    example: 1000
  })
  @IsNumber()
  weight: number;

  @ApiProperty({
    description: 'Giá trị đơn hàng (VNĐ) dùng để tính phí khai giá',
    example: 3000000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  value?: number;

  @ApiProperty({
    description: 'Phương thức vận chuyển (road hoặc fly)',
    example: 'road',
    required: false
  })
  @IsOptional()
  @IsString()
  transport?: string;

  @ApiProperty({
    description: 'Dịch vụ xFast/xTeam (xteam hoặc none)',
    example: 'none'
  })
  @IsString()
  deliverOption: string;

  @ApiProperty({
    description: 'Mảng mã nhãn áp dụng cho đơn',
    example: [1, 7],
    required: false
  })
  @IsOptional()
  @IsArray()
  tags?: number[];
}

/**
 * DTO cho request lấy trạng thái đơn hàng GHTK
 */
export class GetGHTKOrderStatusRequestDto {
  @ApiProperty({
    description: 'Mã vận đơn GHTK hoặc mã đơn đối tác',
    example: 'S1.A1.17373471'
  })
  @IsString()
  trackingOrder: string;
}

/**
 * DTO cho request in nhãn đơn hàng GHTK
 */
export class PrintGHTKLabelRequestDto {
  @ApiProperty({
    description: 'Mã vận đơn GHTK',
    example: 'S1.A1.2001297581'
  })
  @IsString()
  trackingOrder: string;

  @ApiProperty({
    description: 'Kiểu in (portrait: dọc, landscape: ngang)',
    example: 'portrait',
    required: false
  })
  @IsOptional()
  @IsString()
  original?: string;

  @ApiProperty({
    description: 'Kích thước giấy (A5 hoặc A6)',
    example: 'A6',
    required: false
  })
  @IsOptional()
  @IsString()
  paperSize?: string;
}

/**
 * DTO cho request hủy đơn hàng GHTK
 */
export class CancelGHTKOrderRequestDto {
  @ApiProperty({
    description: 'Mã vận đơn GHTK hoặc mã đơn đối tác (với prefix partner_id:)',
    example: 'S1.A1.2001297581'
  })
  @IsString()
  trackingOrder: string;
}

/**
 * DTO cho request lấy danh sách địa chỉ lấy hàng
 */
export class GetGHTKPickupAddressesRequestDto {
  // Không có tham số bổ sung, chỉ cần header
}

/**
 * DTO cho request lấy địa chỉ cấp 4
 */
export class GetGHTKLevel4AddressRequestDto {
  @ApiProperty({
    description: 'Tên tỉnh/thành phố',
    example: 'Hà Nội'
  })
  @IsString()
  province: string;

  @ApiProperty({
    description: 'Tên quận/huyện',
    example: 'Quận Ba Đình'
  })
  @IsString()
  district: string;

  @ApiProperty({
    description: 'Tên đường/phường',
    example: 'Đội Cấn'
  })
  @IsString()
  wardStreet: string;

  @ApiProperty({
    description: 'Thông tin địa chỉ chi tiết cần tìm',
    example: '20',
    required: false
  })
  @IsOptional()
  @IsString()
  address?: string;
}

/**
 * DTO cho request tìm kiếm sản phẩm GHTK
 */
export class SearchGHTKProductRequestDto {
  @ApiProperty({
    description: 'Từ khóa tên sản phẩm cần tìm',
    example: 'laptop'
  })
  @IsString()
  term: string;
}
