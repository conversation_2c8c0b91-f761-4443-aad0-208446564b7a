# GHN Service Usage Examples

## Cài đặt và Cấu hình

### 1. Environment Variables

Thêm vào file `.env`:

```bash
# GHN Shipping Configuration
# IMPORTANT: Get real token and shop ID from: https://sso.ghn.vn/ > Settings > API
# Replace with your actual GHN token and shop ID
GHN_TOKEN=your_real_ghn_token_here
GHN_SHOP_ID=your_real_shop_id_here
GHN_BASE_URL=https://dev-online-gateway.ghn.vn
GHN_TIMEOUT=30000
GHN_TEST_MODE=true
```

**Lưu ý quan trọng:**
- `GHN_TOKEN`: Token API thực từ tài khoản GHN của bạn
- `GHN_SHOP_ID`: Shop ID thực từ cửa hàng GHN đã tạo
- `GHN_TEST_MODE=true`: Sử dụng môi trường test (sandbox)
- `GHN_TEST_MODE=false`: Sử dụng môi trường production

### 2. Import Service

```typescript
import { GHNShipmentService } from '../services/ghn-shipment.service';

@Injectable()
export class YourService {
  constructor(private readonly ghnService: GHNShipmentService) {}
}
```

## Sử dụng trong Code

### 1. Tạo đơn hàng

```typescript
const orderRequest = {
  shopId: 123456,
  toName: "Nguyễn Văn A",
  toPhone: "0912345678",
  toAddress: "123 Đường ABC, Quận 1, TP.HCM",
  toWardCode: "20107",
  toDistrictId: 1442,
  fromName: "Cửa hàng XYZ",
  fromPhone: "0987654321",
  fromAddress: "456 Đường DEF, Quận 2, TP.HCM",
  clientOrderCode: "ORDER_123456",
  codAmount: 500000,
  content: "Laptop Gaming Asus",
  weight: 2000, // gram
  length: 30,   // cm
  width: 20,    // cm
  height: 5,    // cm
  serviceTypeId: 2, // E-commerce
  paymentTypeId: 1, // Người gửi trả phí
  requiredNote: "CHOXEMHANGKHONGTHU",
  note: "Gọi trước khi giao",
  items: [
    {
      name: "Laptop Asus Gaming",
      code: "LAPTOP001",
      quantity: 1,
      price: 15000000
    }
  ]
};

const result = await this.ghnService.createOrder(orderRequest);
console.log('GHN Order Code:', result.data.order_code);
console.log('Total Fee:', result.data.total_fee);
```

### 2. Tính phí vận chuyển

```typescript
const feeRequest = {
  fromDistrictId: 1442,
  fromWardCode: "20107",
  toDistrictId: 1443,
  toWardCode: "20201",
  weight: 1000,
  length: 20,
  width: 15,
  height: 10,
  serviceTypeId: 2,
  codValue: 300000
};

const feeResult = await this.ghnService.calculateFee(feeRequest);
console.log('Shipping Fee:', feeResult.data.total);
console.log('Service Fee:', feeResult.data.service_fee);
console.log('Insurance Fee:', feeResult.data.insurance_fee);
```

### 3. Lấy thông tin đơn hàng

```typescript
const orderCode = "FFFNL9HH";
const orderInfo = await this.ghnService.getOrderInfo(orderCode);

console.log('Order Status:', orderInfo.data[0].status);
console.log('From Name:', orderInfo.data[0].from_name);
console.log('To Name:', orderInfo.data[0].to_name);
```

### 4. Hủy đơn hàng

```typescript
const orderCodes = ["FFFNL9HH", "GGGML8II"];
const cancelResult = await this.ghnService.cancelOrder(orderCodes);

cancelResult.data.forEach(result => {
  console.log(`Order ${result.order_code}: ${result.result ? 'Cancelled' : 'Failed'}`);
});
```

### 5. Lấy danh sách dịch vụ

```typescript
const fromDistrict = 1442; // Quận 1, TP.HCM
const toDistrict = 1443;   // Quận 2, TP.HCM

const services = await this.ghnService.getServices(fromDistrict, toDistrict);
services.data.forEach(service => {
  console.log(`Service: ${service.short_name} (ID: ${service.service_id})`);
});
```

### 6. Lấy địa chỉ

```typescript
// Lấy danh sách tỉnh/thành phố
const provinces = await this.ghnService.getProvinces();
console.log('Provinces:', provinces.data);

// Lấy danh sách quận/huyện
const provinceId = 202; // TP.HCM
const districts = await this.ghnService.getDistricts(provinceId);
console.log('Districts:', districts.data);

// Lấy danh sách phường/xã
const districtId = 1442; // Quận 1
const wards = await this.ghnService.getWards(districtId);
console.log('Wards:', wards.data);
```

### 7. Tính thời gian giao hàng

```typescript
const leadTimeRequest = {
  fromDistrictId: 1442,
  fromWardCode: "20107",
  toDistrictId: 1443,
  toWardCode: "20201",
  serviceId: 53320
};

const leadTime = await this.ghnService.getLeadTime(leadTimeRequest);
const deliveryDate = new Date(leadTime.data.leadtime * 1000);
console.log('Expected Delivery:', deliveryDate);
```

### 8. Xem trước đơn hàng

```typescript
const previewRequest = {
  toName: "Nguyễn Văn A",
  toPhone: "0912345678",
  toAddress: "123 Đường ABC",
  toWardCode: "20107",
  toDistrictId: 1442,
  codAmount: 300000,
  content: "Sách giáo khoa",
  weight: 500,
  length: 20,
  width: 15,
  height: 5,
  serviceTypeId: 2,
  paymentTypeId: 1,
  requiredNote: "KHONGCHOXEMHANG"
};

const preview = await this.ghnService.previewOrder(previewRequest);
console.log('Preview Total Fee:', preview.data.total_fee);
console.log('Expected Delivery:', preview.data.expected_delivery_time);
```

### 9. Cập nhật COD

```typescript
const orderCode = "FFFNL9HH";
const newCodAmount = 600000;

await this.ghnService.updateCOD(orderCode, newCodAmount);
console.log('COD updated successfully');
```

### 10. In nhãn đơn hàng

```typescript
const orderCodes = ["FFFNL9HH"];
const printResult = await this.ghnService.printOrder(orderCodes);
console.log('Print Token:', printResult.data);

// Sử dụng token để in nhãn qua GHN interface
```

## Xử lý Lỗi

### 1. Try-Catch Pattern

```typescript
try {
  const result = await this.ghnService.createOrder(orderRequest);
  return result;
} catch (error) {
  if (error instanceof AppException) {
    switch (error.errorCode) {
      case 'GHN_INVALID_TOKEN':
        console.error('Token GHN không hợp lệ');
        break;
      case 'GHN_API_ERROR':
        console.error('Lỗi API GHN:', error.message);
        break;
      case 'GHN_NETWORK_ERROR':
        console.error('Lỗi kết nối mạng');
        break;
      default:
        console.error('Lỗi không xác định:', error.message);
    }
  }
  throw error;
}
```

### 2. Validation Errors

```typescript
// Kiểm tra trước khi gọi API
if (!orderRequest.toName || !orderRequest.toPhone) {
  throw new Error('Thông tin người nhận không đầy đủ');
}

if (orderRequest.weight > 50000) {
  throw new Error('Khối lượng vượt quá giới hạn 50kg');
}

if (orderRequest.codAmount > 50000000) {
  throw new Error('Số tiền COD vượt quá giới hạn 50 triệu');
}
```

## Webhook Handling

### 1. Xử lý Webhook

```typescript
@Post('webhook/ghn')
async handleGHNWebhook(@Body() webhookData: GHNWebhookDataDto) {
  try {
    await this.ghnService.handleWebhook(webhookData);
    
    // Xử lý logic business
    switch (webhookData.Type) {
      case 'switch_status':
        await this.updateOrderStatus(webhookData.OrderCode, webhookData.Status);
        break;
      case 'update_cod':
        await this.updateOrderCOD(webhookData.OrderCode, webhookData.CODAmount);
        break;
    }
    
    return { success: true };
  } catch (error) {
    console.error('Webhook error:', error);
    return { success: false, error: error.message };
  }
}
```

### 2. Verify Webhook

```typescript
// Có thể thêm logic verify webhook signature nếu GHN hỗ trợ
private verifyWebhookSignature(payload: string, signature: string): boolean {
  // Implementation depends on GHN webhook security
  return true;
}
```

## Best Practices

### 1. Retry Logic

```typescript
async createOrderWithRetry(orderRequest: any, maxRetries = 3): Promise<any> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await this.ghnService.createOrder(orderRequest);
    } catch (error) {
      if (attempt === maxRetries) throw error;
      
      // Exponential backoff
      const delay = Math.pow(2, attempt) * 1000;
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}
```

### 2. Caching

```typescript
@Injectable()
export class GHNCacheService {
  private cache = new Map();
  
  async getProvincesWithCache(): Promise<any> {
    const cacheKey = 'ghn_provinces';
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }
    
    const provinces = await this.ghnService.getProvinces();
    this.cache.set(cacheKey, provinces);
    
    // Cache for 1 hour
    setTimeout(() => this.cache.delete(cacheKey), 3600000);
    
    return provinces;
  }
}
```

### 3. Logging

```typescript
async createOrderWithLogging(orderRequest: any): Promise<any> {
  const startTime = Date.now();
  
  try {
    this.logger.log('Creating GHN order', {
      clientOrderCode: orderRequest.clientOrderCode,
      toName: orderRequest.toName
    });
    
    const result = await this.ghnService.createOrder(orderRequest);
    
    this.logger.log('GHN order created successfully', {
      orderCode: result.data.order_code,
      totalFee: result.data.total_fee,
      duration: Date.now() - startTime
    });
    
    return result;
  } catch (error) {
    this.logger.error('Failed to create GHN order', {
      error: error.message,
      duration: Date.now() - startTime
    });
    throw error;
  }
}
```

## Testing

### 1. Unit Tests

```typescript
describe('GHNShipmentService', () => {
  let service: GHNShipmentService;
  
  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [GHNShipmentService, GHNConfigValidationHelper]
    }).compile();
    
    service = module.get<GHNShipmentService>(GHNShipmentService);
  });
  
  it('should create order successfully', async () => {
    const orderRequest = {
      // ... test data
    };
    
    const result = await service.createOrder(orderRequest);
    expect(result.code).toBe(200);
    expect(result.data.order_code).toBeDefined();
  });
});
```

### 2. Integration Tests

```typescript
describe('GHN Integration', () => {
  it('should calculate fee correctly', async () => {
    const feeRequest = {
      fromDistrictId: 1442,
      toDistrictId: 1443,
      weight: 1000,
      serviceTypeId: 2
    };
    
    const result = await ghnService.calculateFee(feeRequest);
    expect(result.data.total).toBeGreaterThan(0);
  });
});
```
