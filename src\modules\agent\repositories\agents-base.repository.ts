import { Injectable } from '@nestjs/common';
import { DataSource, SelectQueryBuilder } from 'typeorm';
import { BaseAgentRepository } from './base-agent.repository';
import { AgentBase } from '../entities/agents-base.entity';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '../exceptions/agent-error-codes';

/**
 * Repository cho AgentBase
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến agent base
 */
@Injectable()
export class AgentsBaseRepository extends BaseAgentRepository<AgentBase> {
  constructor(dataSource: DataSource) {
    super(AgentBase, dataSource, 'AgentsBaseRepository');
  }

  /**
   * Tạo query builder c<PERSON> bản cho AgentBase
   * @returns SelectQueryBuilder cho AgentBase
   */
  protected createBaseQuery(): SelectQueryBuilder<AgentBase> {
    return this.createQueryBuilder('agentBase')
      .select([
        'agentBase.id',
        'agentBase.active',
        'agentBase.modelRegistryId',
        'agentBase.modelName',
        'agentBase.keyLlmId',
        'agentBase.createdBy',
        'agentBase.updatedBy',
        'agentBase.createdAt',
        'agentBase.updatedAt'
      ]);
  }

  /**
   * Tìm agent base đang active
   * @returns AgentBase đang active hoặc null
   */
  async findActiveAgent(): Promise<AgentBase | null> {
    return this.createBaseQueryWithSoftDelete()
      .andWhere('agentBase.active = :active', { active: true })
      .getOne();
  }

  /**
   * Kiểm tra có agent nào đang active không
   * @returns true nếu có agent active, false nếu không
   */
  async hasActiveAgent(): Promise<boolean> {
    const count = await this.createBaseQueryWithSoftDelete()
      .andWhere('agentBase.active = :active', { active: true })
      .getCount();

    return count > 0;
  }

  /**
   * Set agent thành active và deactivate tất cả agents khác
   * @param agentId ID của agent cần set active
   * @param updatedBy ID của user thực hiện update
   */
  async setActiveAgent(agentId: string, updatedBy?: number): Promise<void> {
    // Kiểm tra agent có tồn tại không
    const exists = await this.existsById(agentId);
    if (!exists) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // Sử dụng transaction để đảm bảo consistency
    await this.dataSource.transaction(async (manager) => {
      // 1. Deactivate tất cả agents khác
      await manager
        .createQueryBuilder()
        .update(AgentBase)
        .set({
          active: false,
          updatedAt: Date.now(),
          updatedBy: updatedBy
        })
        .where('active = :active', { active: true })
        .andWhere('deleted_at IS NULL')
        .execute();

      // 2. Activate agent được chọn
      await manager
        .createQueryBuilder()
        .update(AgentBase)
        .set({
          active: true,
          updatedAt: Date.now(),
          updatedBy: updatedBy
        })
        .where('id = :id', { id: agentId })
        .execute();
    });

    this.logger.log(`Set agent ${agentId} as active`);
  }

  /**
   * Deactivate agent hiện tại
   * @param agentId ID của agent cần deactivate
   * @param updatedBy ID của user thực hiện update
   */
  async deactivateAgent(agentId: string, updatedBy?: number): Promise<void> {
    const result = await this.createQueryBuilder()
      .update()
      .set({
        active: false,
        updatedBy: updatedBy
      })
      .where('id = :id', { id: agentId })
      .andWhere('active = :active', { active: true })
      .execute();

    if (result.affected === 0) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    this.logger.log(`Deactivated agent ${agentId}`);
  }

  /**
   * Tìm agent base theo model registry ID
   * @param modelRegistryId ID của model registry
   * @returns Danh sách AgentBase
   */
  async findByModelRegistryId(modelRegistryId: string): Promise<AgentBase[]> {
    return this.createBaseQueryWithSoftDelete()
      .andWhere('agentBase.modelRegistryId = :modelRegistryId', { modelRegistryId })
      .getMany();
  }

  /**
   * Tìm agent base theo key LLM ID
   * @param keyLlmId ID của key LLM
   * @returns Danh sách AgentBase
   */
  async findByKeyLlmId(keyLlmId: string): Promise<AgentBase[]> {
    return this.createBaseQueryWithSoftDelete()
      .andWhere('agentBase.keyLlmId = :keyLlmId', { keyLlmId })
      .getMany();
  }

  /**
   * Tạo agent base mới
   * @param agentBaseData Dữ liệu agent base
   * @param createdBy ID của user tạo
   * @returns AgentBase đã được tạo
   */
  async createAgentBase(
    agentBaseData: Partial<AgentBase>,
    createdBy?: number
  ): Promise<AgentBase> {
    // Nếu agent mới được set active, cần deactivate agents khác trước
    if (agentBaseData.active) {
      await this.dataSource.transaction(async (manager) => {
        // Deactivate tất cả agents khác
        await manager
          .createQueryBuilder()
          .update(AgentBase)
          .set({
            active: false,
            updatedAt: Date.now(),
            createdAt: Date.now(),
          })
          .where('active = :active', { active: true })
          .andWhere('deleted_at IS NULL')
          .execute();

        // Tạo agent mới
        const result = await manager
          .createQueryBuilder()
          .insert()
          .into(AgentBase)
          .values({
            ...agentBaseData,
            createdBy,
            updatedBy: createdBy
          })
          .execute();
      });
    } else {
      // Tạo agent không active
      await this.createWithTimestamp(agentBaseData, createdBy);
    }

    return this.findById(agentBaseData.id as string, false) as Promise<AgentBase>;
  }

  /**
   * Cập nhật agent base
   * @param id ID của agent base
   * @param updateData Dữ liệu cập nhật
   * @param updatedBy ID của user thực hiện update
   * @returns AgentBase đã được cập nhật
   */
  async updateAgentBase(
    id: string,
    updateData: Partial<AgentBase>,
    updatedBy?: number
  ): Promise<AgentBase> {
    // Kiểm tra agent có tồn tại không
    const exists = await this.existsById(id);
    if (!exists) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // Nếu update active = true, cần deactivate agents khác
    if (updateData.active === true) {
      await this.setActiveAgent(id, updatedBy);
    } else {
      // Update bình thường
      await this.createQueryBuilder()
        .update()
        .set({
          ...updateData,
          updatedBy
        })
        .where('id = :id', { id })
        .execute();
    }

    return this.findById(id, false) as Promise<AgentBase>;
  }

  /**
   * Soft delete agent base với validation
   * @param id ID của agent base
   * @param deletedBy ID của user thực hiện xóa
   */
  async softDeleteAgentBase(id: string, deletedBy?: number): Promise<void> {
    // Kiểm tra agent có đang active không
    const agent = await this.findById(id);
    if (!agent) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    if (agent.active) {
      throw new AppException(AGENT_ERROR_CODES.CANNOT_DELETE_ACTIVE_AGENT);
    }

    await this.softDeleteById(id, deletedBy);
  }

  /**
   * Restore agent base đã bị soft delete
   * @param id ID của agent base
   */
  async restoreAgentBase(id: string): Promise<void> {
    // Kiểm tra agent có tồn tại trong soft deleted không
    const agent = await this.findById(id, false);
    if (!agent || !agent.deletedAt) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    await this.restoreById(id);
  }
}
