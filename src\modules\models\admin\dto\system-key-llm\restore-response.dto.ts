import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho response khôi phục system key LLM
 */
export class RestoreSystemKeyLlmResponseDto {
  @ApiProperty({
    description: 'Danh sách ID đã khôi phục thành công',
    example: ['123e4567-e89b-12d3-a456-426614174000', '456e7890-e89b-12d3-a456-426614174001'],
    type: [String]
  })
  restored: string[];

  @ApiProperty({
    description: 'Danh sách ID không thể khôi phục (không tồn tại hoặc chưa bị xóa)',
    example: ['789e0123-e89b-12d3-a456-426614174002'],
    type: [String]
  })
  failed: string[];

  @ApiProperty({
    description: 'Tổng số ID được yêu cầu khôi phục',
    example: 3
  })
  totalRequested: number;

  @ApiProperty({
    description: 'Số ID đã khôi phục thành công',
    example: 2
  })
  totalRestored: number;

  @ApiProperty({
    description: 'Số ID không thể khôi phục',
    example: 1
  })
  totalFailed: number;
}
