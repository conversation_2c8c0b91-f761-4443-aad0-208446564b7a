import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder, ObjectLiteral } from 'typeorm';
import { PaginatedResult } from '@common/response';
import { QueryDto } from '@common/dto';
import { SqlHelper } from '@common/helpers/sql.helper';

/**
 * Base Repository cho Agent Module
 * Cung cấp các phương thức cơ bản cho tất cả repositories trong agent module
 *
 * @template T Entity type
 */
@Injectable()
export abstract class BaseAgentRepository<T extends ObjectLiteral> extends Repository<T> {
  protected readonly logger: Logger;
  protected readonly sqlHelper: SqlHelper;

  constructor(
    entity: new () => T,
    protected readonly dataSource: DataSource,
    loggerName?: string,
  ) {
    super(entity, dataSource.createEntityManager());
    this.logger = new Logger(loggerName || this.constructor.name);
    this.sqlHelper = new SqlHelper(dataSource, { enableLogging: false });
  }

  /**
   * Tạo query builder cơ bản cho entity
   * Phương thức abstract - phải được implement bởi các repository con
   *
   * @returns SelectQueryBuilder cho entity
   */
  protected abstract createBaseQuery(): SelectQueryBuilder<T>;

  /**
   * Tạo query builder với soft delete filter
   * Tự động loại bỏ các bản ghi đã bị soft delete
   *
   * @param alias Alias cho entity (optional)
   * @returns SelectQueryBuilder với soft delete filter
   */
  protected createBaseQueryWithSoftDelete(alias?: string): SelectQueryBuilder<T> {
    const qb = this.createBaseQuery();
    const entityAlias = alias || qb.alias;

    // Thêm filter cho soft delete
    qb.andWhere(`${entityAlias}.deletedAt IS NULL`);

    return qb;
  }

  /**
   * Tìm entity theo ID
   * Tự động áp dụng soft delete filter
   *
   * @param id ID của entity
   * @param withSoftDelete Có áp dụng soft delete filter không (default: true)
   * @returns Entity hoặc null
   */
  async findById(id: string, withSoftDelete: boolean = true): Promise<T | null> {
    const qb = withSoftDelete ? this.createBaseQueryWithSoftDelete() : this.createBaseQuery();

    return qb
      .where(`${qb.alias}.id = :id`, { id })
      .getOne();
  }

  /**
   * Kiểm tra entity có tồn tại theo ID không
   * Tự động áp dụng soft delete filter
   *
   * @param id ID của entity
   * @param withSoftDelete Có áp dụng soft delete filter không (default: true)
   * @returns true nếu tồn tại, false nếu không
   */
  async existsById(id: string, withSoftDelete: boolean = true): Promise<boolean> {
    const qb = withSoftDelete ? this.createBaseQueryWithSoftDelete() : this.createBaseQuery();

    const count = await qb
      .where(`${qb.alias}.id = :id`, { id })
      .getCount();

    return count > 0;
  }

  /**
   * Lấy danh sách entities với phân trang
   * Sử dụng SqlHelper để xử lý pagination
   *
   * @param query Tham số query (page, limit, search, sort)
   * @param options Tùy chọn cho pagination
   * @returns Kết quả phân trang
   */
  async findPaginated(
    query: QueryDto,
    options?: {
      alias?: string;
      selectFields?: (keyof T)[];
      searchFields?: (keyof T)[];
      customize?: (qb: SelectQueryBuilder<T>) => SelectQueryBuilder<T>;
      withSoftDelete?: boolean;
    },
  ): Promise<PaginatedResult<T>> {
    const withSoftDelete = options?.withSoftDelete ?? true;

    return this.sqlHelper.getPaginatedData(this as Repository<T>, query, {
      alias: options?.alias,
      selectFields: options?.selectFields,
      searchFields: options?.searchFields,
      customize: (qb: SelectQueryBuilder<T>) => {
        // Áp dụng soft delete filter nếu cần
        if (withSoftDelete) {
          const alias = options?.alias || qb.alias;
          qb.andWhere(`${alias}.deletedAt IS NULL`);
        }

        // Áp dụng custom logic nếu có
        if (options?.customize) {
          qb = options.customize(qb);
        }

        return qb;
      },
    });
  }

  /**
   * Soft delete entity theo ID
   * Cập nhật deletedAt và deletedBy
   *
   * @param id ID của entity
   * @param deletedBy ID của user thực hiện xóa
   * @returns Kết quả update
   */
  async softDeleteById(id: string, deletedBy?: number): Promise<void> {
    const updateData: any = {
      deletedAt: Date.now(),
    };

    if (deletedBy) {
      updateData.deletedBy = deletedBy;
    }

    await this.createQueryBuilder()
      .update()
      .set(updateData)
      .where('id = :id', { id })
      .execute();

    this.logger.log(`Soft deleted entity with ID: ${id}`);
  }

  /**
   * Restore entity đã bị soft delete
   * Xóa deletedAt và deletedBy
   *
   * @param id ID của entity
   * @returns Kết quả update
   */
  async restoreById(id: string): Promise<void> {
    await this.createQueryBuilder()
      .update()
      .set({
        deletedAt: () => 'NULL',
        deletedBy: () => 'NULL',
      } as any)
      .where('id = :id', { id })
      .execute();

    this.logger.log(`Restored entity with ID: ${id}`);
  }

  /**
   * Lấy danh sách entities đã bị soft delete
   *
   * @param query Tham số query
   * @returns Kết quả phân trang của entities đã xóa
   */
  async findDeleted(query: QueryDto): Promise<PaginatedResult<T>> {
    return this.sqlHelper.getPaginatedData(this as Repository<T>, query, {
      customize: (qb: SelectQueryBuilder<T>) => {
        qb.andWhere(`${qb.alias}.deletedAt IS NOT NULL`);
        return qb;
      },
    });
  }

  /**
   * Đếm số lượng entities
   *
   * @param withSoftDelete Có áp dụng soft delete filter không (default: true)
   * @returns Số lượng entities
   */
  async countEntities(withSoftDelete: boolean = true): Promise<number> {
    const qb = withSoftDelete ? this.createBaseQueryWithSoftDelete() : this.createBaseQuery();
    return qb.getCount();
  }

  /**
   * Tìm tất cả entities
   *
   * @param withSoftDelete Có áp dụng soft delete filter không (default: true)
   * @returns Danh sách entities
   */
  async findAll(withSoftDelete: boolean = true): Promise<T[]> {
    const qb = withSoftDelete ? this.createBaseQueryWithSoftDelete() : this.createBaseQuery();
    return qb.getMany();
  }

  /**
   * Cập nhật timestamp updated_at
   *
   * @param id ID của entity
   * @param updatedBy ID của user thực hiện cập nhật
   */
  protected async updateTimestamp(id: string, updatedBy?: number): Promise<void> {
    const updateData: any = {
      updatedAt: Date.now(),
    };

    if (updatedBy) {
      updateData.updatedBy = updatedBy;
    }

    await this.createQueryBuilder()
      .update()
      .set(updateData)
      .where('id = :id', { id })
      .execute();
  }

  /**
   * Tạo entity mới với timestamp
   *
   * @param entityData Dữ liệu entity
   * @param createdBy ID của user tạo
   * @returns Entity đã được tạo
   */
  protected async createWithTimestamp(entityData: Partial<T>, createdBy?: number): Promise<T> {
    const now = Date.now();
    const data = {
      ...entityData,
      createdAt: now,
      updatedAt: now,
    } as any;

    if (createdBy) {
      data.createdBy = createdBy;
      data.updatedBy = createdBy;
    }

    const result = await this.createQueryBuilder()
      .insert()
      .values(data)
      .execute();

    return this.findById(result.identifiers[0].id, false) as Promise<T>;
  }
}
