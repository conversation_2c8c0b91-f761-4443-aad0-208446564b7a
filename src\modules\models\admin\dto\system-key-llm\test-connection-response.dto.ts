import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho response test connection API key
 */
export class TestConnectionResponseDto {
  @ApiProperty({
    description: 'Trạng thái kết nối',
    example: true
  })
  success: boolean;

  @ApiProperty({
    description: 'Thông báo kết quả',
    example: 'Kết nối API key thành công'
  })
  message: string;

  @ApiPropertyOptional({
    description: 'Thông tin lỗi (nếu có)',
    example: 'Invalid API key',
    nullable: true
  })
  error?: string;
}
