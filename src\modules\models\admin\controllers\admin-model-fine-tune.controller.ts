import { CurrentEmployee } from '@/modules/auth/decorators';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { QueryDto } from '@common/dto';
import { ApiResponseDto } from '@common/response';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';
import { AdminModelFineTuneService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý API cho Admin Model Fine Tune
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_MODEL_FINE_TUNING)
@Controller('admin/model-fine-tune')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class AdminModelFineTuneController {
  constructor(private readonly adminModelFineTuneService: AdminModelFineTuneService) { }

  /**
   * Tạo mới model fine tune
   */
  @Post()
  @ApiOperation({ summary: 'Tạo mới model fine tune' })
  @ApiResponse({
    status: 201,
    description: 'Tạo mới model fine tune thành công',
    type: ApiResponseDto
  })
  create(
    @Body() createDto: any, // TODO: Tạo CreateModelFineTuneDto
    @CurrentEmployee('id') employeeId: number
  ) {
    return this.adminModelFineTuneService.create(createDto, employeeId);
  }

  /**
   * Lấy danh sách model fine tune có phân trang và tìm kiếm
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách model fine tune có phân trang và tìm kiếm',
    description: 'API này hỗ trợ tìm kiếm theo tên model, phân trang và sắp xếp'
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách model fine tune',
    type: ApiResponseDto
  })
  findAll(@Query() queryDto: QueryDto) {
    return this.adminModelFineTuneService.findAll(queryDto);
  }

  /**
   * Lấy chi tiết model fine tune
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết model fine tune' })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết model fine tune',
    type: ApiResponseDto
  })
  findOne(@Param('id') id: string) {
    return this.adminModelFineTuneService.findOne(id);
  }

  /**
   * Cập nhật model fine tune
   */
  @Patch(':id')
  @ApiOperation({ summary: 'Cập nhật model fine tune' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật model fine tune thành công',
    type: ApiResponseDto
  })
  update(
    @Param('id') id: string,
    @Body() updateDto: any, // TODO: Tạo UpdateModelFineTuneDto
    @CurrentEmployee('id') employeeId: number
  ) {
    return this.adminModelFineTuneService.update(id, updateDto, employeeId);
  }

  /**
   * Xóa model fine tune (soft delete)
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Xóa model fine tune' })
  @ApiResponse({
    status: 200,
    description: 'Xóa model fine tune thành công',
    type: ApiResponseDto
  })
  remove(
    @Param('id') id: string,
    @CurrentEmployee('id') employeeId: number
  ) {
    return this.adminModelFineTuneService.remove(id, employeeId);
  }

  /**
   * Lấy danh sách model fine tune đã xóa
   */
  @Get('deleted/list')
  @ApiOperation({ summary: 'Lấy danh sách model fine tune đã xóa' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách model fine tune đã xóa',
    type: ApiResponseDto
  })
  findDeleted(@Query() queryDto: QueryDto) {
    return this.adminModelFineTuneService.findDeleted(queryDto);
  }

  /**
   * Khôi phục model fine tune đã xóa
   */
  @Patch(':id/restore')
  @ApiOperation({ summary: 'Khôi phục model fine tune đã xóa' })
  @ApiResponse({
    status: 200,
    description: 'Khôi phục model fine tune thành công',
    type: ApiResponseDto
  })
  restore(
    @Param('id') id: string,
    @CurrentEmployee('id') employeeId: number
  ) {
    return this.adminModelFineTuneService.restore(id, employeeId);
  }

  /**
   * Lấy lịch sử fine tune của model
   */
  @Get(':id/history')
  @ApiOperation({ summary: 'Lấy lịch sử fine tune của model' })
  @ApiResponse({
    status: 200,
    description: 'Lịch sử fine tune của model',
    type: ApiResponseDto
  })
  getHistory(@Param('id') id: string) {
    return this.adminModelFineTuneService.getHistory(id);
  }
}
