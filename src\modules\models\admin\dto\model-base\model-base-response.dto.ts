import { ProviderEnumq } from '@/shared/services/ai/utils/type-provider.util';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';

/**
 * DTO cho response của model base
 */
@Exclude()
export class ModelBaseResponseDto {
  /**
   * UUID của model base
   */
  @ApiProperty({
    description: 'UUID của model base',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Expose()
  id: string;

  /**
   * ID của system key LLM đượ<PERSON> sử dụng
   */
  @ApiProperty({
    description: 'ID của system key LLM được sử dụng',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Expose()
  systemKeyLlmId: string;

  /**
   * ID model từ nhà cung cấp
   */
  @ApiProperty({
    description: 'ID model từ nhà cung cấp',
    example: 'gpt-4-turbo-preview',
  })
  @Expose()
  modelId: string;

  /**
   * Tên hiển thị của model
   */
  @ApiProperty({
    description: 'Tên hiển thị của model',
    example: 'GPT-4 Turbo Preview',
  })
  @Expose()
  name: string;

  /**
   * Mô tả về model
   */
  @ApiPropertyOptional({
    description: 'Mô tả về model',
    example: 'Model GPT-4 Turbo với khả năng xử lý văn bản và hình ảnh',
  })
  @Expose()
  description?: string;

  /**
   * Nhà cung cấp AI
   */
  @ApiProperty({
    description: 'Nhà cung cấp AI',
    enum: ProviderEnumq,
    example: ProviderEnumq.OPENAI,
  })
  @Expose()
  provider: ProviderEnumq;

  /**
   * Số token tối đa
   */
  @ApiPropertyOptional({
    description: 'Số token tối đa',
    example: 4096,
  })
  @Expose()
  maxTokens?: number;

  /**
   * Kích thước context window
   */
  @ApiPropertyOptional({
    description: 'Kích thước context window',
    example: 128000,
  })
  @Expose()
  contextWindow?: number;

  /**
   * Chi phí input per 1k tokens (USD)
   */
  @ApiPropertyOptional({
    description: 'Chi phí input per 1k tokens (USD)',
    example: 0.01,
  })
  @Expose()
  inputCostPer1kTokens?: number;

  /**
   * Chi phí output per 1k tokens (USD)
   */
  @ApiPropertyOptional({
    description: 'Chi phí output per 1k tokens (USD)',
    example: 0.03,
  })
  @Expose()
  outputCostPer1kTokens?: number;

  /**
   * Có cho phép user sử dụng không
   */
  @ApiProperty({
    description: 'Có cho phép user sử dụng không',
    example: true,
  })
  @Expose()
  isUserAccessible: boolean;

  /**
   * Có cho phép fine-tuning không
   */
  @ApiProperty({
    description: 'Có cho phép fine-tuning không',
    example: false,
  })
  @Expose()
  isFineTunable: boolean;

  /**
   * Metadata bổ sung
   */
  @ApiPropertyOptional({
    description: 'Metadata bổ sung (JSON)',
    example: { version: '1.0', features: ['chat', 'completion'] },
  })
  @Expose()
  metadata?: any;

  /**
   * Thời gian tạo (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian tạo (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  createdAt: number;

  /**
   * Người tạo
   */
  @ApiPropertyOptional({
    description: 'ID người tạo',
    example: 1,
  })
  @Expose()
  createdBy?: number;

  /**
   * Tên người tạo
   */
  @ApiPropertyOptional({
    description: 'Tên người tạo',
    example: 'John Doe',
  })
  @Expose()
  createdByName?: string | null;

  /**
   * Thời gian cập nhật (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  updatedAt: number;

  /**
   * Người cập nhật
   */
  @ApiPropertyOptional({
    description: 'ID người cập nhật',
    example: 1,
  })
  @Expose()
  updatedBy?: number;

  /**
   * Tên người cập nhật
   */
  @ApiPropertyOptional({
    description: 'Tên người cập nhật',
    example: 'Jane Smith',
  })
  @Expose()
  updatedByName?: string | null;

  /**
   * Thời gian xóa (epoch millis)
   */
  @ApiPropertyOptional({
    description: 'Thời gian xóa (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  deletedAt?: number | null;

  /**
   * Người xóa
   */
  @ApiPropertyOptional({
    description: 'ID người xóa',
    example: 1,
  })
  @Expose()
  deletedBy?: number | null;

  /**
   * Tên người xóa
   */
  @ApiPropertyOptional({
    description: 'Tên người xóa',
    example: 'Admin User',
  })
  @Expose()
  deletedByName?: string | null;

  /**
   * Tên system key LLM
   */
  @ApiPropertyOptional({
    description: 'Tên system key LLM được sử dụng',
    example: 'OpenAI Production Key',
  })
  @Expose()
  systemKeyLlmName?: string | null;
}
