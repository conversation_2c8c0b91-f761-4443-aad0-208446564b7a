import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, IsBoolean } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';
import { ProviderEnum } from '@/modules/models/constants';

/**
 * Enum cho các trường có thể sắp xếp
 */
export enum ModelBaseSortBy {
  NAME = 'name',
  PROVIDER = 'provider',
  STATUS = 'status',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  INPUT_COST = 'inputCostPer1kTokens',
  OUTPUT_COST = 'outputCostPer1kTokens',
  MAX_TOKENS = 'maxTokens',
  CONTEXT_WINDOW = 'contextWindow',
}

/**
 * DTO cho việc truy vấn danh sách model base
 */
export class ModelBaseQueryDto extends QueryDto {
  /**
   * Tìm kiếm theo tên model
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên model',
    example: 'GPT-4',
  })
  @IsOptional()
  @IsString()
  name?: string;

  /**
   * Lọc theo nhà cung cấp
   */
  @ApiPropertyOptional({
    description: 'Lọc theo nhà cung cấp',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
  })
  @IsOptional()
  @IsEnum(ProviderEnum)
  provider?: ProviderEnum;

  /**
   * Lọc theo khả năng user access
   */
  @ApiPropertyOptional({
    description: 'Lọc theo khả năng user access',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isUserAccessible?: boolean;

  /**
   * Lọc theo khả năng fine-tuning
   */
  @ApiPropertyOptional({
    description: 'Lọc theo khả năng fine-tuning',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isFineTunable?: boolean;

  /**
   * Tìm kiếm theo model ID từ provider
   */
  @ApiPropertyOptional({
    description: 'Tìm kiếm theo model ID từ provider',
    example: 'gpt-4',
  })
  @IsOptional()
  @IsString()
  modelId?: string;

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: ModelBaseSortBy,
    example: ModelBaseSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(ModelBaseSortBy)
  sortBy?: ModelBaseSortBy = ModelBaseSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;
}
