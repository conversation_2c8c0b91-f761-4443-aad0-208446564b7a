import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { UserAddress } from '../entities';

/**
 * Repository cho UserAddress
 */
@Injectable()
export class UserAddressRepository extends Repository<UserAddress> {
  private readonly logger = new Logger(UserAddressRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserAddress, dataSource.createEntityManager());
  }

  /**
   * Tìm địa chỉ theo ID và userId
   * @param id ID địa chỉ
   * @param userId ID người dùng
   * @returns Địa chỉ hoặc null
   */
  async findByIdAndUserId(id: number, userId: number): Promise<UserAddress | null> {
    try {
      this.logger.log(`Tìm địa chỉ với ID=${id} và userId=${userId}`);
      
      const address = await this.findOne({
        where: { id, userId, isActive: true }
      });
      
      return address;
    } catch (error) {
      this.logger.error(`Lỗi khi tìm địa chỉ: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm địa chỉ: ${error.message}`);
    }
  }

  /**
   * Lấy danh sách địa chỉ của user
   * @param userId ID người dùng
   * @returns Danh sách địa chỉ
   */
  async findByUserId(userId: number): Promise<UserAddress[]> {
    try {
      this.logger.log(`Lấy danh sách địa chỉ của userId=${userId}`);
      
      const addresses = await this.find({
        where: { userId, isActive: true },
        order: { isDefault: 'DESC', createdAt: 'DESC' }
      });
      
      return addresses;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách địa chỉ: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi lấy danh sách địa chỉ: ${error.message}`);
    }
  }

  /**
   * Lấy địa chỉ mặc định của user
   * @param userId ID người dùng
   * @returns Địa chỉ mặc định hoặc null
   */
  async findDefaultByUserId(userId: number): Promise<UserAddress | null> {
    try {
      this.logger.log(`Lấy địa chỉ mặc định của userId=${userId}`);
      
      const address = await this.findOne({
        where: { userId, isDefault: true, isActive: true }
      });
      
      return address;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy địa chỉ mặc định: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi lấy địa chỉ mặc định: ${error.message}`);
    }
  }

  /**
   * Tạo địa chỉ mới
   * @param addressData Dữ liệu địa chỉ
   * @returns Địa chỉ đã tạo
   */
  async createAddress(addressData: Partial<UserAddress>): Promise<UserAddress> {
    try {
      this.logger.log(`Tạo địa chỉ mới cho userId=${addressData.userId}`);
      
      // Nếu đây là địa chỉ mặc định, bỏ mặc định của các địa chỉ khác
      if (addressData.isDefault) {
        await this.update(
          { userId: addressData.userId, isDefault: true },
          { isDefault: false }
        );
      }
      
      const address = this.create(addressData);
      const savedAddress = await this.save(address);
      
      this.logger.log(`Đã tạo địa chỉ với ID=${savedAddress.id}`);
      return savedAddress;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo địa chỉ: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tạo địa chỉ: ${error.message}`);
    }
  }

  /**
   * Cập nhật địa chỉ
   * @param id ID địa chỉ
   * @param userId ID người dùng
   * @param updateData Dữ liệu cập nhật
   * @returns Địa chỉ đã cập nhật
   */
  async updateAddress(id: number, userId: number, updateData: Partial<UserAddress>): Promise<UserAddress> {
    try {
      this.logger.log(`Cập nhật địa chỉ ID=${id} cho userId=${userId}`);
      
      // Kiểm tra địa chỉ có tồn tại không
      const existingAddress = await this.findByIdAndUserId(id, userId);
      if (!existingAddress) {
        throw new Error('Địa chỉ không tồn tại hoặc không thuộc về bạn');
      }
      
      // Nếu đây là địa chỉ mặc định, bỏ mặc định của các địa chỉ khác
      if (updateData.isDefault) {
        await this.update(
          { userId, isDefault: true, id: { $ne: id } as any },
          { isDefault: false }
        );
      }
      
      // Cập nhật timestamp
      updateData.updatedAt = Date.now();
      
      await this.update({ id, userId }, updateData);
      
      const updatedAddress = await this.findByIdAndUserId(id, userId);
      this.logger.log(`Đã cập nhật địa chỉ ID=${id}`);
      
      return updatedAddress!;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật địa chỉ: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi cập nhật địa chỉ: ${error.message}`);
    }
  }

  /**
   * Xóa địa chỉ (soft delete)
   * @param id ID địa chỉ
   * @param userId ID người dùng
   * @returns Kết quả xóa
   */
  async deleteAddress(id: number, userId: number): Promise<boolean> {
    try {
      this.logger.log(`Xóa địa chỉ ID=${id} cho userId=${userId}`);
      
      // Kiểm tra địa chỉ có tồn tại không
      const existingAddress = await this.findByIdAndUserId(id, userId);
      if (!existingAddress) {
        throw new Error('Địa chỉ không tồn tại hoặc không thuộc về bạn');
      }
      
      // Soft delete
      await this.update(
        { id, userId },
        { isActive: false, updatedAt: Date.now() }
      );
      
      this.logger.log(`Đã xóa địa chỉ ID=${id}`);
      return true;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa địa chỉ: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi xóa địa chỉ: ${error.message}`);
    }
  }

  /**
   * Đặt địa chỉ làm mặc định
   * @param id ID địa chỉ
   * @param userId ID người dùng
   * @returns Địa chỉ đã cập nhật
   */
  async setAsDefault(id: number, userId: number): Promise<UserAddress> {
    try {
      this.logger.log(`Đặt địa chỉ ID=${id} làm mặc định cho userId=${userId}`);
      
      // Kiểm tra địa chỉ có tồn tại không
      const existingAddress = await this.findByIdAndUserId(id, userId);
      if (!existingAddress) {
        throw new Error('Địa chỉ không tồn tại hoặc không thuộc về bạn');
      }
      
      // Bỏ mặc định của tất cả địa chỉ khác
      await this.update(
        { userId, isDefault: true },
        { isDefault: false }
      );
      
      // Đặt địa chỉ này làm mặc định
      await this.update(
        { id, userId },
        { isDefault: true, updatedAt: Date.now() }
      );
      
      const updatedAddress = await this.findByIdAndUserId(id, userId);
      this.logger.log(`Đã đặt địa chỉ ID=${id} làm mặc định`);
      
      return updatedAddress!;
    } catch (error) {
      this.logger.error(`Lỗi khi đặt địa chỉ mặc định: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi đặt địa chỉ mặc định: ${error.message}`);
    }
  }
}
