import { CdnService } from '@/shared/services/cdn.service';
import { TimeIntervalEnum } from '@/shared/utils';
import { UserDataFineTune } from '../../entities/user-data-fine-tune.entity';
import { UserDataFineTuneResponseDto, UserDataFineTuneDetialResponseDto } from '../dto/user-data-fine-tune/user-data-fine-tune-response.dto';

/**
 * Mapper cho UserDataFineTune
 * Chuyển đổi giữa entity và DTO
 */
export class UserDataFineTuneMapper {
  /**
   * Chuyển đổi entity sang response DTO cho danh sách
   * @param entity UserDataFineTune entity
   * @param cdnService Service để tạo URL CDN
   * @returns UserDataFineTuneResponseDto
   */
  static toResponseDto(
    entity: UserDataFineTune,
    cdnService?: CdnService
  ): UserDataFineTuneResponseDto {
    return {
      id: entity.id,
      name: entity.name,
      description: entity.description,
      createdAt: entity.createdAt,
      status: entity.status,
    };
  }

  /**
   * Chuyển đổi entity sang detail response DTO
   * @param entity UserDataFineTune entity
   * @param cdnService Service để tạo URL CDN
   * @returns UserDataFineTuneDetialResponseDto
   */
  static async toDetailResponseDto(
    entity: UserDataFineTune,
    cdnService: CdnService
  ): Promise<UserDataFineTuneDetialResponseDto> {
    let trainDatasetUrl = '';
    let validDatasetUrl: string | null = null;

    try {
      // Tạo URL cho train dataset
      if (entity.trainDataset) {
        const trainUrl = cdnService.generateUrlView(entity.trainDataset, TimeIntervalEnum.ONE_HOUR);
        trainDatasetUrl = trainUrl || '';
      }

      // Tạo URL cho valid dataset nếu có
      if (entity.validDataset) {
        const validUrl = cdnService.generateUrlView(entity.validDataset, TimeIntervalEnum.ONE_HOUR);
        validDatasetUrl = validUrl || null;
      }
    } catch (error) {
      console.warn(`Lỗi tạo URL CDN cho dataset ${entity.id}:`, error.message);
    }

    return {
      id: entity.id,
      name: entity.name,
      description: entity.description,
      createdAt: entity.createdAt,
      status: entity.status,
      trainDatasetUrl,
      validDatasetUrl,
    };
  }

  /**
   * Chuyển đổi danh sách entity sang danh sách response DTO
   * @param entities Danh sách UserDataFineTune entity
   * @param cdnService Service để tạo URL CDN
   * @returns Danh sách UserDataFineTuneResponseDto
   */
  static toResponseDtoList(
    entities: UserDataFineTune[],
    cdnService?: CdnService
  ): UserDataFineTuneResponseDto[] {
    return entities.map(entity => this.toResponseDto(entity, cdnService));
  }
}
