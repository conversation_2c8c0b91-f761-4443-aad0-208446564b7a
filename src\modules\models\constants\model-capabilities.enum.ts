export enum InputModalityEnum {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
}

export enum OutputModalityEnum {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
}

export enum SamplingParameterEnum {
  TEMPERATURE = 'temperature',
  TOP_P = 'top_p',
  TOP_K = 'top_k',
}

export enum FeatureEnum {
  STRUCTURED_OUTPUT = 'structured_output',
  TOOL_CALL = 'tool_call',
}