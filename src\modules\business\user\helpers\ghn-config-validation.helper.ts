import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { IGHNConfig } from '@modules/business/interfaces/ghn.interface';
import {
  GHN_BASE_URLS,
  GHN_TEST_CONFIG,
  GHN_TIMEOUT,
  GHN_ERROR_MESSAGES
} from '@modules/business/constants/ghn.constants';

/**
 * Helper để validate và xây dựng cấu hình GHN
 */
@Injectable()
export class GHNConfigValidationHelper {
  private readonly logger = new Logger(GHNConfigValidationHelper.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Validate và xây dựng cấu hình GHN từ environment variables
   */
  validateAndBuildConfig(): IGHNConfig {
    try {
      this.logger.log('Bắt đầu validate cấu hình <PERSON>N');

      // Lấy các giá trị từ environment variables
      const token = this.configService.get<string>('GHN_TOKEN');
      const shopId = this.configService.get<string>('GHN_SHOP_ID');
      const baseUrl = this.configService.get<string>('GHN_BASE_URL');
      const timeout = this.configService.get<number>('GHN_TIMEOUT');
      const isTestMode = this.configService.get<string>('GHN_TEST_MODE') === 'true';

      // Log warning nếu sử dụng giá trị mặc định
      if (!token) {
        this.logger.warn('GHN_TOKEN không được cấu hình, sử dụng giá trị mặc định');
      }
      if (!shopId) {
        this.logger.warn('GHN_SHOP_ID không được cấu hình, sử dụng giá trị mặc định');
      }

      // Xây dựng cấu hình với fallback values
      const config: IGHNConfig = {
        token: token || GHN_TEST_CONFIG.TOKEN,
        shopId: shopId || GHN_TEST_CONFIG.SHOP_ID,
        baseUrl: baseUrl || this.getBaseUrl(isTestMode),
        timeout: timeout || GHN_TIMEOUT,
        isTestMode: isTestMode !== undefined ? isTestMode : true
      };

      // Validate cấu hình
      this.validateConfig(config);

      this.logger.log('Cấu hình GHN hợp lệ', {
        baseUrl: config.baseUrl,
        isTestMode: config.isTestMode,
        hasToken: !!config.token,
        hasShopId: !!config.shopId,
        tokenSource: token ? 'environment' : 'default',
        shopIdSource: shopId ? 'environment' : 'default'
      });

      return config;
    } catch (error) {
      this.logger.error('Lỗi khi validate cấu hình GHN:', error);
      throw error;
    }
  }

  /**
   * Validate cấu hình GHN
   */
  private validateConfig(config: IGHNConfig): void {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate token
    if (!config.token || config.token.trim() === '') {
      errors.push('Token GHN không được để trống');
    } else if (config.token === GHN_TEST_CONFIG.TOKEN) {
      warnings.push('Đang sử dụng token GHN mặc định - cần cập nhật token thực từ GHN');
    }

    // Validate shop ID
    if (!config.shopId || config.shopId.trim() === '') {
      errors.push('Shop ID GHN không được để trống');
    } else if (config.shopId === GHN_TEST_CONFIG.SHOP_ID) {
      warnings.push('Đang sử dụng Shop ID GHN mặc định - cần cập nhật Shop ID thực từ GHN');
    }

    // Validate base URL
    if (!config.baseUrl || !this.isValidUrl(config.baseUrl)) {
      errors.push('Base URL GHN không hợp lệ');
    }

    // Validate timeout
    if (config.timeout && (config.timeout < 1000 || config.timeout > 300000)) {
      errors.push('Timeout phải từ 1000ms đến 300000ms');
    }

    // Log warnings
    if (warnings.length > 0) {
      warnings.forEach(warning => this.logger.warn(warning));
    }

    if (errors.length > 0) {
      throw new AppException(
        BUSINESS_ERROR_CODES.GHN_INVALID_CONFIG,
        `Cấu hình GHN không hợp lệ: ${errors.join(', ')}`
      );
    }
  }

  /**
   * Lấy base URL dựa trên môi trường
   */
  private getBaseUrl(isTestMode?: boolean): string {
    return isTestMode !== false ? GHN_BASE_URLS.TEST : GHN_BASE_URLS.PRODUCTION;
  }

  /**
   * Kiểm tra URL có hợp lệ không
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate token format (có thể mở rộng thêm logic validate)
   */
  validateTokenFormat(token: string): boolean {
    if (!token || token.trim() === '') {
      return false;
    }

    // Có thể thêm logic validate format token cụ thể của GHN
    // Hiện tại chỉ kiểm tra không rỗng
    return token.length > 10; // Token GHN thường dài hơn 10 ký tự
  }

  /**
   * Validate shop ID format
   */
  validateShopIdFormat(shopId: string): boolean {
    if (!shopId || shopId.trim() === '') {
      return false;
    }

    // Shop ID thường là số
    return /^\d+$/.test(shopId);
  }

  /**
   * Tạo cấu hình test cho development
   */
  createTestConfig(): IGHNConfig {
    this.logger.warn('Sử dụng cấu hình test GHN - chỉ dành cho development');

    return {
      token: GHN_TEST_CONFIG.TOKEN,
      shopId: GHN_TEST_CONFIG.SHOP_ID,
      baseUrl: GHN_TEST_CONFIG.BASE_URL,
      timeout: GHN_TIMEOUT,
      isTestMode: true
    };
  }

  /**
   * Log cảnh báo về cấu hình
   */
  logConfigWarnings(config: IGHNConfig): void {
    if (config.token === GHN_TEST_CONFIG.TOKEN) {
      this.logger.warn('Đang sử dụng token test GHN - không dành cho production');
    }

    if (config.shopId === GHN_TEST_CONFIG.SHOP_ID) {
      this.logger.warn('Đang sử dụng shop ID test GHN - không dành cho production');
    }

    if (config.isTestMode) {
      this.logger.warn('GHN đang ở chế độ test');
    }

    if (config.timeout && config.timeout < 10000) {
      this.logger.warn('Timeout GHN thấp có thể gây lỗi kết nối');
    }
  }

  /**
   * Kiểm tra cấu hình có phải production không
   */
  isProductionConfig(config: IGHNConfig): boolean {
    return !config.isTestMode &&
           config.token !== GHN_TEST_CONFIG.TOKEN &&
           config.shopId !== GHN_TEST_CONFIG.SHOP_ID &&
           config.baseUrl === GHN_BASE_URLS.PRODUCTION;
  }

  /**
   * Sanitize cấu hình để log (ẩn thông tin nhạy cảm)
   */
  sanitizeConfigForLogging(config: IGHNConfig): any {
    return {
      baseUrl: config.baseUrl,
      timeout: config.timeout,
      isTestMode: config.isTestMode,
      hasToken: !!config.token,
      tokenLength: config.token?.length || 0,
      hasShopId: !!config.shopId,
      shopIdLength: config.shopId?.length || 0
    };
  }

  /**
   * Validate cấu hình GHN và trả về kết quả chi tiết
   */
  validateGHNConfig(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    config: IGHNConfig;
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Lấy cấu hình từ environment
    const token = this.configService.get<string>('GHN_TOKEN');
    const shopId = this.configService.get<string>('GHN_SHOP_ID');
    const baseUrl = this.configService.get<string>('GHN_BASE_URL');
    const timeout = this.configService.get<number>('GHN_TIMEOUT');
    const isTestMode = this.configService.get<string>('GHN_TEST_MODE') === 'true';

    // Validate token
    if (!token) {
      warnings.push('GHN_TOKEN không được cấu hình, sử dụng token test mặc định');
    } else if (token === GHN_TEST_CONFIG.TOKEN) {
      warnings.push('Đang sử dụng GHN token test, không phù hợp cho production');
    } else if (!this.validateTokenFormat(token)) {
      errors.push('GHN_TOKEN có format không hợp lệ');
    }

    // Validate shop ID
    if (!shopId) {
      warnings.push('GHN_SHOP_ID không được cấu hình, sử dụng shop ID test mặc định');
    } else if (shopId === GHN_TEST_CONFIG.SHOP_ID) {
      warnings.push('Đang sử dụng GHN shop ID test, không phù hợp cho production');
    } else if (!this.validateShopIdFormat(shopId)) {
      errors.push('GHN_SHOP_ID có format không hợp lệ');
    }

    // Validate base URL
    if (!baseUrl) {
      warnings.push('GHN_BASE_URL không được cấu hình, sử dụng URL mặc định');
    } else if (!this.isValidUrl(baseUrl)) {
      errors.push('GHN_BASE_URL có format không hợp lệ');
    }

    // Validate timeout
    if (timeout && (timeout < 1000 || timeout > 300000)) {
      warnings.push('GHN_TIMEOUT nên trong khoảng 1000-300000ms');
    }

    // Validate test mode
    if (isTestMode === false && token === GHN_TEST_CONFIG.TOKEN) {
      errors.push('Không thể sử dụng token test khi GHN_TEST_MODE=false');
    }

    // Production warnings
    if (!isTestMode) {
      if (token === GHN_TEST_CONFIG.TOKEN) {
        errors.push('Production mode không thể sử dụng token test');
      }
      if (shopId === GHN_TEST_CONFIG.SHOP_ID) {
        warnings.push('Production mode đang sử dụng shop ID test');
      }
    }

    const config: IGHNConfig = {
      token: token || GHN_TEST_CONFIG.TOKEN,
      shopId: shopId || GHN_TEST_CONFIG.SHOP_ID,
      baseUrl: baseUrl || this.getBaseUrl(isTestMode),
      timeout: timeout || GHN_TIMEOUT,
      isTestMode: isTestMode ?? true
    };

    const isValid = errors.length === 0;

    // Log validation results
    if (errors.length > 0) {
      this.logger.error('GHN configuration validation failed:', errors);
    }
    if (warnings.length > 0) {
      this.logger.warn('GHN configuration warnings:', warnings);
    }
    if (isValid) {
      this.logger.log('GHN configuration validation passed', {
        hasCustomToken: token !== GHN_TEST_CONFIG.TOKEN,
        hasCustomShopId: shopId !== GHN_TEST_CONFIG.SHOP_ID,
        isTestMode: config.isTestMode
      });
    }

    return {
      isValid,
      errors,
      warnings,
      config
    };
  }

  /**
   * Lấy thông tin môi trường hiện tại
   */
  getEnvironmentInfo(): {
    nodeEnv: string;
    isProduction: boolean;
    isDevelopment: boolean;
    isTest: boolean;
  } {
    const nodeEnv = this.configService.get<string>('NODE_ENV') || 'development';

    return {
      nodeEnv,
      isProduction: nodeEnv === 'production',
      isDevelopment: nodeEnv === 'development',
      isTest: nodeEnv === 'test'
    };
  }

  /**
   * Kiểm tra cấu hình có phù hợp với môi trường không
   */
  validateEnvironmentCompatibility(): {
    isCompatible: boolean;
    recommendations: string[];
  } {
    const envInfo = this.getEnvironmentInfo();
    const validation = this.validateGHNConfig();
    const recommendations: string[] = [];

    // Production environment checks
    if (envInfo.isProduction) {
      if (validation.config.isTestMode) {
        recommendations.push('Production environment nên set GHN_TEST_MODE=false');
      }
      if (validation.config.token === GHN_TEST_CONFIG.TOKEN) {
        recommendations.push('Production environment cần token GHN thực tế');
      }
      if (validation.config.shopId === GHN_TEST_CONFIG.SHOP_ID) {
        recommendations.push('Production environment cần shop ID GHN thực tế');
      }
    }

    // Development environment checks
    if (envInfo.isDevelopment) {
      if (!validation.config.isTestMode) {
        recommendations.push('Development environment nên set GHN_TEST_MODE=true');
      }
    }

    // Test environment checks
    if (envInfo.isTest) {
      if (!validation.config.isTestMode) {
        recommendations.push('Test environment nên set GHN_TEST_MODE=true');
      }
    }

    return {
      isCompatible: !recommendations.length,
      recommendations
    };
  }

  /**
   * Tạo báo cáo cấu hình GHN
   */
  generateConfigReport(): string {
    const validation = this.validateGHNConfig();
    const envInfo = this.getEnvironmentInfo();
    const compatibility = this.validateEnvironmentCompatibility();

    let report = '=== GHN Configuration Report ===\n\n';

    // Environment info
    report += `Environment: ${envInfo.nodeEnv}\n`;
    report += `Test Mode: ${validation.config.isTestMode}\n`;
    report += `Base URL: ${validation.config.baseUrl}\n`;
    report += `Timeout: ${validation.config.timeout}ms\n`;
    report += `Has Custom Token: ${validation.config.token !== GHN_TEST_CONFIG.TOKEN}\n`;
    report += `Has Custom Shop ID: ${validation.config.shopId !== GHN_TEST_CONFIG.SHOP_ID}\n\n`;

    // Validation status
    report += `Validation Status: ${validation.isValid ? 'PASSED' : 'FAILED'}\n`;

    if (validation.errors.length > 0) {
      report += `Errors:\n${validation.errors.map(e => `  - ${e}`).join('\n')}\n`;
    }

    if (validation.warnings.length > 0) {
      report += `Warnings:\n${validation.warnings.map(w => `  - ${w}`).join('\n')}\n`;
    }

    // Environment compatibility
    report += `\nEnvironment Compatibility: ${compatibility.isCompatible ? 'COMPATIBLE' : 'NEEDS ATTENTION'}\n`;

    if (compatibility.recommendations.length > 0) {
      report += `Recommendations:\n${compatibility.recommendations.map(r => `  - ${r}`).join('\n')}\n`;
    }

    return report;
  }
}
