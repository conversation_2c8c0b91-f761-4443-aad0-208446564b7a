import { SWAGGER_API_TAGS } from '@/common/swagger';
import { CurrentEmployee } from '@/modules/auth/decorators';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { PermissionsGuard } from '@/modules/auth/guards/permissions.guard';
import { ApiResponseDto } from '@common/response';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import {
  CreateModelBaseDto,
  ModelBaseQueryDto,
  UpdateModelBaseDto
} from '../dto/model-base';
import { AdminModelBaseService } from '../services';

// /**
//  * Controller xử lý API cho Admin Model Base
//  */
@ApiTags(SWAGGER_API_TAGS.ADMIN_BASE_MODEL)
@Controller('admin/model-base')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@ApiBearerAuth('JWT-auth')
export class AdminModelBaseController {
  constructor(private readonly adminModelBaseService: AdminModelBaseService) { }

//   /**
//    * Tạo mới model base
//    */
//   @Post()
//   @ApiOperation({ summary: 'Tạo mới model base' })
//   @ApiResponse({
//     status: 201,
//     description: 'Tạo mới model base thành công',
//     type: ApiResponseDto
//   })
//   create(
//     @Body() createDto: CreateModelBaseDto,
//     @CurrentEmployee('id') employeeId: number
//   ) {
//     return this.adminModelBaseService.create(createDto, employeeId);
//   }

//   /**
//    * Lấy danh sách model base có phân trang và tìm kiếm
//    */
//   @Get()
//   @ApiOperation({
//     summary: 'Lấy danh sách model base có phân trang và tìm kiếm',
//     description: 'API này hỗ trợ tìm kiếm theo tên model, phân trang và sắp xếp'
//   })
//   @ApiResponse({
//     status: 200,
//     description: 'Danh sách model base',
//     type: ApiResponseDto
//   })
//   findAll(@Query() queryDto: ModelBaseQueryDto) {
//     return this.adminModelBaseService.findAll(queryDto);
//   }

//   /**
//    * Lấy chi tiết model base
//    */
//   @Get(':id')
//   @ApiOperation({ summary: 'Lấy chi tiết model base' })
//   @ApiResponse({
//     status: 200,
//     description: 'Chi tiết model base',
//     type: ApiResponseDto
//   })
//   findOne(@Param('id') id: string) {
//     return this.adminModelBaseService.findOne(id);
//   }

//   /**
//    * Cập nhật model base
//    */
//   @Patch(':id')
//   @ApiOperation({ summary: 'Cập nhật model base' })
//   @ApiResponse({
//     status: 200,
//     description: 'Cập nhật model base thành công',
//     type: ApiResponseDto
//   })
//   update(
//     @Param('id') id: string,
//     @Body() updateDto: UpdateModelBaseDto,
//     @CurrentEmployee('id') employeeId: number
//   ) {
//     return this.adminModelBaseService.update(id, updateDto, employeeId);
//   }

//   /**
//    * Xóa model base (soft delete)
//    */
//   @Delete(':id')
//   @ApiOperation({ summary: 'Xóa model base' })
//   @ApiResponse({
//     status: 200,
//     description: 'Xóa model base thành công',
//     type: ApiResponseDto
//   })
//   remove(
//     @Param('id') id: string,
//     @CurrentEmployee('id') employeeId: number
//   ) {
//     return this.adminModelBaseService.remove(id, employeeId);
//   }

//   /**
//    * Lấy danh sách model base đã xóa
//    */
//   @Get('deleted/list')
//   @ApiOperation({ summary: 'Lấy danh sách model base đã xóa' })
//   @ApiResponse({
//     status: 200,
//     description: 'Danh sách model base đã xóa',
//     type: ApiResponseDto
//   })
//   findDeleted(@Query() queryDto: ModelBaseQueryDto) {
//     return this.adminModelBaseService.findDeleted(queryDto);
//   }

//   /**
//    * Khôi phục model base đã xóa
//    */
//   @Patch(':id/restore')
//   @ApiOperation({ summary: 'Khôi phục model base đã xóa' })
//   @ApiResponse({
//     status: 200,
//     description: 'Khôi phục model base thành công',
//     type: ApiResponseDto
//   })
//   restore(
//     @Param('id') id: string,
//     @CurrentEmployee('id') employeeId: number
//   ) {
//     return this.adminModelBaseService.restore(id, employeeId);
//   }

//   /**
//    * Quản lý API keys cho model base
//    */
//   @Post(':id/api-keys')
//   @ApiOperation({ summary: 'Thêm API key cho model base' })
//   @ApiResponse({
//     status: 200,
//     description: 'Thêm API key thành công',
//     type: ApiResponseDto
//   })
//   addApiKey(
//     @Param('id') id: string,
//     @Body() apiKeyDto: any, // TODO: Tạo AddApiKeyDto
//     @CurrentEmployee('id') employeeId: number
//   ) {
//     return this.adminModelBaseService.addApiKey(id, apiKeyDto, employeeId);
//   }

//   /**
//    * Xóa API key khỏi model base
//    */
//   @Delete(':id/api-keys/:keyId')
//   @ApiOperation({ summary: 'Xóa API key khỏi model base' })
//   @ApiResponse({
//     status: 200,
//     description: 'Xóa API key thành công',
//     type: ApiResponseDto
//   })
//   removeApiKey(
//     @Param('id') id: string,
//     @Param('keyId') keyId: string,
//     @CurrentEmployee('id') employeeId: number
//   ) {
//     return this.adminModelBaseService.removeApiKey(id, keyId, employeeId);
//   }
}
