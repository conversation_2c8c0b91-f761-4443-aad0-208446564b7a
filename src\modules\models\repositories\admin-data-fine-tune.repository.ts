import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminDataFineTune } from '../entities/admin-data-fine-tune.entity';
import { PaginatedResult } from '@common/response';
import { AdminDataFineTuneQueryDto } from '../admin/dto/data-fine-tune';
import { DataFineTuneStatus } from '../constants/data-fine-tune-status.enum';

/**
 * Repository cho AdminDataFineTune
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến admin data fine-tune
 */
@Injectable()
export class AdminDataFineTuneRepository extends Repository<AdminDataFineTune> {
  private readonly logger = new Logger(AdminDataFineTuneRepository.name);

  constructor(private dataSource: DataSource) {
    super(AdminDataFineTune, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho AdminDataFineTune
   * @returns SelectQueryBuilder cho AdminDataFineTune
   */
  createBaseQuery(): SelectQueryBuilder<AdminDataFineTune> {
    return this.createQueryBuilder('adminDataFineTune')
      .select([
        'adminDataFineTune.id',
        'adminDataFineTune.name',
        'adminDataFineTune.description',
        'adminDataFineTune.createdAt',
        'adminDataFineTune.updatedAt',
        'adminDataFineTune.createdBy',
        'adminDataFineTune.updatedBy'
      ])
      .where('adminDataFineTune.deletedAt IS NULL');
  }

  /**
   * Tìm kiếm admin data fine-tune với phân trang
   * @param queryDto DTO query
   * @returns Kết quả phân trang
   */
  async findWithPagination(queryDto: AdminDataFineTuneQueryDto): Promise<PaginatedResult<AdminDataFineTune>> {
    const query = this.createBaseQuery();

    query.select([
      'adminDataFineTune.id',
      'adminDataFineTune.name',
      'adminDataFineTune.description',
      'adminDataFineTune.createdAt',
      'adminDataFineTune.updatedAt',
      'adminDataFineTune.status'
    ]);

    // Tìm kiếm theo tên
    if (queryDto.search) {
      query.andWhere('adminDataFineTune.name ILIKE :name OR adminDataFineTune.description ILIKE :name', {
        name: `%${queryDto.search}%`
      });
    }

    // Lọc theo trạng thái
    if (queryDto.status) {
      query.andWhere('adminDataFineTune.status = :status', { status: queryDto.status });
    }

    // Sắp xếp
    if (queryDto.sortBy) {
      const direction = queryDto.sortDirection || 'ASC';
      query.orderBy(`adminDataFineTune.${queryDto.sortBy}`, direction);
    } else {
      query.orderBy('adminDataFineTune.createdAt', 'DESC');
    }

    // Phân trang
    const skip = (queryDto.page - 1) * queryDto.limit;
    query.skip(skip).take(queryDto.limit);

    const [items, totalItems] = await query.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: queryDto.limit,
        totalPages: Math.ceil(totalItems / queryDto.limit),
        currentPage: queryDto.page
      }
    };
  }

  /**
   * Tìm admin data fine-tune theo ID với đầy đủ dữ liệu
   * @param id ID của dataset
   * @returns AdminDataFineTune hoặc null
   */
  async findByIdWithFullData(id: string): Promise<AdminDataFineTune | null> {
    return this.createQueryBuilder('adminDataFineTune')
      .select([
        'adminDataFineTune.id',
        'adminDataFineTune.name',
        'adminDataFineTune.description',
        'adminDataFineTune.trainDataset',
        'adminDataFineTune.validDataset',
        'adminDataFineTune.status',
        'adminDataFineTune.createdAt',
        'adminDataFineTune.updatedAt',
        'adminDataFineTune.createdBy',
        'adminDataFineTune.updatedBy'
      ])
      .where('adminDataFineTune.id = :id', { id })
      .andWhere('adminDataFineTune.deletedAt IS NULL')
      .getOne();
  }

  /**
   * Kiểm tra tồn tại theo tên (để tránh trùng lặp)
   * @param name Tên dataset
   * @param excludeId ID cần loại trừ (cho trường hợp update)
   * @returns true nếu tồn tại
   */
  async existsByName(name: string, excludeId?: string): Promise<boolean> {
    const query = this.createQueryBuilder('adminDataFineTune')
      .where('adminDataFineTune.name = :name', { name })
      .andWhere('adminDataFineTune.deletedAt IS NULL');

    if (excludeId) {
      query.andWhere('adminDataFineTune.id != :excludeId', { excludeId });
    }

    const count = await query.getCount();
    return count > 0;
  }

  /**
   * Tìm các dataset đã xóa với phân trang
   * @param queryDto DTO query
   * @returns Kết quả phân trang
   */
  async findDeletedWithPagination(queryDto: AdminDataFineTuneQueryDto): Promise<PaginatedResult<AdminDataFineTune>> {
    const query = this.createQueryBuilder('adminDataFineTune')
      .select([
        'adminDataFineTune.id',
        'adminDataFineTune.name',
        'adminDataFineTune.description',
        'adminDataFineTune.createdAt',
        'adminDataFineTune.updatedAt',
        'adminDataFineTune.deletedAt',
        'adminDataFineTune.createdBy',
        'adminDataFineTune.updatedBy',
        'adminDataFineTune.deletedBy'
      ])
      .where('adminDataFineTune.deletedAt IS NOT NULL');

    // Tìm kiếm theo tên
    if (queryDto.search) {
      query.andWhere('adminDataFineTune.name ILIKE :search', {
        search: `%${queryDto.search}%`
      });
    }

    // Sắp xếp theo thời gian xóa
    query.orderBy('adminDataFineTune.deletedAt', 'DESC');

    // Phân trang
    const skip = (queryDto.page - 1) * queryDto.limit;
    query.skip(skip).take(queryDto.limit);

    const [items, totalItems] = await query.getManyAndCount();

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: queryDto.limit,
        totalPages: Math.ceil(totalItems / queryDto.limit),
        currentPage: queryDto.page
      }
    };
  }

  /**
   * Soft delete dataset
   * @param id ID của dataset
   * @param deletedBy ID người xóa
   * @returns true nếu thành công
   */
  async softDeleteDataset(id: string, deletedBy: number): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .update(AdminDataFineTune)
      .set({
        deletedAt: Date.now(),
        deletedBy
      })
      .where('id = :id', { id })
      .andWhere('deletedAt IS NULL')
      .execute();

    return (result.affected || 0) > 0;
  }

  /**
   * Lấy danh sách ID hợp lệ (tồn tại và chưa bị xóa)
   * @param ids Danh sách ID cần kiểm tra
   * @returns Danh sách ID hợp lệ
   */
  async findValidIds(ids: string[]): Promise<string[]> {
    if (ids.length === 0) return [];

    const validDatasets = await this.createQueryBuilder('adminDataFineTune')
      .select(['adminDataFineTune.id'])
      .where('adminDataFineTune.id IN (:...ids)', { ids })
      .andWhere('adminDataFineTune.deletedAt IS NULL')
      .getMany();

    return validDatasets.map(dataset => dataset.id);
  }

  /**
   * Bulk soft delete datasets
   * @param ids Danh sách ID cần xóa
   * @param deletedBy ID người xóa
   * @returns Số lượng record đã xóa
   */
  async bulkSoftDeleteDatasets(ids: string[], deletedBy: number): Promise<number> {
    if (ids.length === 0) return 0;

    const result = await this.createQueryBuilder()
      .update(AdminDataFineTune)
      .set({
        deletedAt: Date.now(),
        deletedBy
      })
      .where('id IN (:...ids)', { ids })
      .andWhere('deletedAt IS NULL')
      .execute();

    return result.affected ?? 0;
  }

  /**
   * Cập nhật trạng thái dataset
   * @param id ID của dataset
   * @param status Trạng thái mới
   * @param updatedBy ID người cập nhật
   * @returns true nếu thành công
   */
  async updateStatus(id: string, status: DataFineTuneStatus, updatedBy: number): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .update(AdminDataFineTune)
      .set({
        status,
        updatedBy,
        updatedAt: Date.now()
      })
      .where('id = :id', { id })
      .andWhere('deletedAt IS NULL')
      .execute();

    return (result.affected ?? 0) > 0;
  }

  /**
   * Bulk cập nhật trạng thái datasets
   * @param ids Danh sách ID
   * @param status Trạng thái mới
   * @param updatedBy ID người cập nhật
   * @returns Số lượng records đã cập nhật
   */
  async bulkUpdateStatus(ids: string[], status: DataFineTuneStatus, updatedBy: number): Promise<number> {
    if (ids.length === 0) return 0;

    const result = await this.createQueryBuilder()
      .update(AdminDataFineTune)
      .set({
        status,
        updatedBy,
        updatedAt: Date.now()
      })
      .where('id IN (:...ids)', { ids })
      .andWhere('deletedAt IS NULL')
      .execute();

    return result.affected ?? 0;
  }

  /**
   * Lấy danh sách datasets theo trạng thái
   * @param status Trạng thái cần lọc
   * @returns Danh sách datasets
   */
  async findByStatus(status: DataFineTuneStatus): Promise<AdminDataFineTune[]> {
    return this.createQueryBuilder('adminDataFineTune')
      .select([
        'adminDataFineTune.id',
        'adminDataFineTune.name',
        'adminDataFineTune.status',
        'adminDataFineTune.trainDataset',
        'adminDataFineTune.validDataset'
      ])
      .where('adminDataFineTune.status = :status', { status })
      .andWhere('adminDataFineTune.deletedAt IS NULL')
      .getMany();
  }

  /**
   * Khôi phục dataset đã xóa
   * @param id ID của dataset
   * @returns true nếu thành công
   */
  async restoreCustom(id: string): Promise<boolean> {
    const result = await this.createQueryBuilder()
      .update(AdminDataFineTune)
      .set({
        deletedAt: null,
        deletedBy: null
      })
      .where('id = :id', { id })
      .andWhere('deletedAt IS NOT NULL')
      .execute();

    return result.affected ? result.affected > 0 : false;
  }
}
