import { ProviderEnum } from '@/modules/models/constants';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, MaxLength, IsBoolean, IsNumber, Min, Max } from 'class-validator';

/**
 * DTO cho việc tạo mới system key LLM
 */
export class CreateSystemKeyLlmDto {
  @ApiProperty({
    description: 'Tên định danh cho key',
    example: 'OpenAI Production Key',
    maxLength: 255,
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Nhà cung cấp model',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
  })
  @IsEnum(ProviderEnum)
  @IsNotEmpty()
  provider: ProviderEnum;

  @ApiProperty({
    description: 'API Key truy cập LLM',
    example: 'sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
  })
  @IsString()
  @IsNotEmpty()
  apiKey: string;
}
