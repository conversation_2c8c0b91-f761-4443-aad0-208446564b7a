import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';
import { ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho thông tin model config
 * Đã loại bỏ các trường deprecated: modelId, providerName
 */
export class ModelConfigResponseDto implements ModelConfig {
  /**
   * Giá trị temperature cho model (0-2)
   */
  @ApiPropertyOptional({
    description: 'Giá trị temperature cho model (0-2)',
    example: 1.0,
  })
  temperature?: number;

  /**
   * Giá trị top_p cho model (0-1)
   */
  @ApiPropertyOptional({
    description: 'Giá trị top_p cho model (0-1)',
    example: 1.0,
  })
  top_p?: number;

  /**
   * Giá trị top_k cho model
   */
  @ApiPropertyOptional({
    description: 'Giá trị top_k cho model',
    example: 1.0,
  })
  top_k?: number;

  /**
   * <PERSON>ố token tối đa cho kết quả
   */
  @ApiPropertyOptional({
    description: '<PERSON>ố token tối đa cho kết quả',
    example: 1000,
  })
  max_tokens?: number;
}
