import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { ModelBaseKeyLlm } from '../entities/model-base-key-llm.entity';

/**
 * Repository cho ModelBaseKeyLlm
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến mapping model base và system key LLM
 */
@Injectable()
export class ModelBaseKeyLlmRepository extends Repository<ModelBaseKeyLlm> {
  private readonly logger = new Logger(ModelBaseKeyLlmRepository.name);

  constructor(private dataSource: DataSource) {
    super(ModelBaseKeyLlm, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho ModelBaseKeyLlm
   * @returns SelectQueryBuilder cho ModelBaseKeyLlm
   */
  private createBaseQuery(): SelectQueryBuilder<ModelBaseKeyLlm> {
    return this.createQueryBuilder('modelBaseKeyLlm');
  }
}
