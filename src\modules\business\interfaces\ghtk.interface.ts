/**
 * Interfaces cho GHTK API
 */

/**
 * Interface cho cấu hình GHTK
 */
export interface IGHTKConfig {
  token: string;
  partnerCode?: string;
  baseUrl: string;
  timeout?: number;
  isTestMode?: boolean;
}

/**
 * Interface cho sản phẩm GHTK
 */
export interface IGHTKProduct {
  name: string;
  price?: number;
  weight: number;
  quantity?: number;
  product_code?: string;
}

/**
 * Interface cho thông tin đơn hàng GHTK
 */
export interface IGHTKOrder {
  id: string;
  pick_name: string;
  pick_address: string;
  pick_province: string;
  pick_district: string;
  pick_ward: string;
  pick_tel: string;
  name: string;
  address: string;
  province: string;
  district: string;
  ward: string;
  hamlet?: string;
  tel: string;
  is_freeship?: string;
  pick_date?: string;
  pick_money?: number;
  note?: string;
  value: number;
  transport?: string;
  pick_option?: string;
  deliver_option?: string;
  tags?: number[];
  sub_tags?: number[];
}

/**
 * Interface cho request tạo đơn hàng GHTK
 */
export interface IGHTKCreateOrderRequest {
  products: IGHTKProduct[];
  order: IGHTKOrder;
}

/**
 * Interface cho response chung của GHTK
 */
export interface IGHTKBaseResponse {
  success: boolean;
  message: string;
  rid?: string;
  code?: number;
}

/**
 * Interface cho response tạo đơn hàng GHTK
 */
export interface IGHTKCreateOrderResponse extends IGHTKBaseResponse {
  order: {
    partner_id: string;
    label: string;
    area: string;
    fee: string;
    insurance_fee: string;
    tracking_id: number;
    estimated_pick_time: string;
    estimated_deliver_time: string;
    products: any[];
    status_id: number;
  };
}

/**
 * Interface cho response tính phí GHTK
 */
export interface IGHTKCalculateFeeResponse extends IGHTKBaseResponse {
  fee: {
    name: string;
    fee: number;
    insurance_fee: number;
    delivery_type: string;
    a: number;
    dt: string;
    extFees: Array<{
      display: string;
      title: string;
      amount: number;
      type: string;
    }>;
    delivery: boolean;
  };
}

/**
 * Interface cho response trạng thái đơn hàng GHTK
 */
export interface IGHTKOrderStatusResponse extends IGHTKBaseResponse {
  order: {
    label_id: string;
    partner_id: string;
    status: string;
    status_text: string;
    created: string;
    modified: string;
    message: string;
    pick_date: string;
    deliver_date: string;
    customer_fullname: string;
    customer_tel: string;
    address: string;
    storage_day: number;
    ship_money: number;
    insurance: number;
    value: number;
    weight: number;
    pick_money: number;
    is_freeship: number;
    status_id: number;
  };
}

/**
 * Interface cho response hủy đơn hàng GHTK
 */
export interface IGHTKCancelOrderResponse extends IGHTKBaseResponse {
  log_id: string;
}

/**
 * Interface cho response địa chỉ lấy hàng GHTK
 */
export interface IGHTKPickupAddressesResponse extends IGHTKBaseResponse {
  data: Array<{
    pick_address_id: string;
    address: string;
    pick_tel: string;
    pick_name: string;
  }>;
}

/**
 * Interface cho response địa chỉ cấp 4 GHTK
 */
export interface IGHTKLevel4AddressResponse extends IGHTKBaseResponse {
  data: string[];
}

/**
 * Interface cho response tìm kiếm sản phẩm GHTK
 */
export interface IGHTKSearchProductResponse extends IGHTKBaseResponse {
  data: Array<{
    full_name: string;
    product_code: string;
    weigh: number;
    cost: number;
  }>;
}

/**
 * Interface cho response giải pháp GHTK
 */
export interface IGHTKSolutionsResponse extends IGHTKBaseResponse {
  data: Array<{
    solution_id: number;
    description: string;
    group_name: string;
  }>;
}



/**
 * Interface cho GHTK Service
 */
export interface IGHTKService {
  // Configuration
  setConfig(config: IGHTKConfig): void;
  getConfig(): IGHTKConfig;

  // Solutions
  getSolutions(): Promise<any>;

  // Orders
  createOrder(request: any): Promise<any>;
  calculateFee(params: any): Promise<any>;
  getOrderStatus(trackingOrder: string): Promise<any>;
  cancelOrder(trackingOrder: string): Promise<any>;
  printLabel(trackingOrder: string, options?: any): Promise<Buffer>;

  // Addresses
  getPickupAddresses(): Promise<any>;
  getLevel4Address(params: any): Promise<any>;

  // Products
  searchProducts(term: string): Promise<any>;

  // Webhook
  handleWebhook(data: any): Promise<void>;
}
