import { ProviderEnum } from '@/modules/models/constants';
import { QueryDto, SortDirection } from '@common/dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';

export enum SystemKeyLlmSortBy {
  NAME = 'name',
  PROVIDER = 'provider',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * DTO query cho system key LLM
 */
export class SystemKeyLlmQueryDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Trường cần sắp xếp',
    example: 'createdAt',
    required: false,
  })
  @IsOptional()
  @IsEnum(SystemKeyLlmSortBy)
  @IsOptional()
  sortBy?: SystemKeyLlmSortBy = SystemKeyLlmSortBy.CREATED_AT;

  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    example: SortDirection.DESC,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;

  @ApiPropertyOptional({
    description: 'Lọc theo nhà cung cấp',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
  })
  @IsOptional()
  @IsEnum(ProviderEnum)
  provider?: ProviderEnum;
}
