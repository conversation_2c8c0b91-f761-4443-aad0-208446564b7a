import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';
import { ProviderEnumq } from '@/shared/services/ai/utils/type-provider.util';

/**
 * DTO cho response của model registry
 */
@Exclude()
export class ModelRegistryResponseDto {
  /**
   * UUID của registry
   */
  @ApiProperty({
    description: 'UUID của registry',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @Expose()
  id: string;

  /**
   * Nhà cung cấp model
   */
  @ApiProperty({
    description: 'Nhà cung cấp model',
    enum: ProviderEnumq,
    example: ProviderEnumq.OPENAI,
  })
  @Expose()
  provider: ProviderEnumq;

  /**
   * Tên mẫu đại diện của model
   */
  @ApiProperty({
    description: 'Tên mẫu đại diện của model',
    example: 'gpt-4*',
  })
  @Expose()
  modelNamePattern: string;

  /**
   * <PERSON><PERSON>c loại dữ liệu đầu vào hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu vào hỗ trợ',
    example: ['text', 'image'],
    type: [String],
  })
  @Expose()
  inputModalities?: string[];

  /**
   * Các loại dữ liệu đầu ra hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các loại dữ liệu đầu ra hỗ trợ',
    example: ['text'],
    type: [String],
  })
  @Expose()
  outputModalities?: string[];

  /**
   * Các tham số sampling
   */
  @ApiPropertyOptional({
    description: 'Các tham số sampling',
    example: [
      { name: 'temperature', type: 'number', min: 0, max: 2, default: 1 }
    ],
    type: [Object],
  })
  @Expose()
  samplingParameters?: any[];

  /**
   * Tập hợp feature đặc biệt
   */
  @ApiPropertyOptional({
    description: 'Tập hợp feature đặc biệt',
    example: ['tool-use', 'function-calling'],
    type: [String],
  })
  @Expose()
  features?: string[];

  /**
   * Thời gian tạo (epoch millis)
   */
  @ApiProperty({
    description: 'Thời gian tạo (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  createdAt: number;

  /**
   * Người tạo
   */
  @ApiPropertyOptional({
    description: 'ID người tạo',
    example: 1,
  })
  @Expose()
  createdBy?: number;

  /**
   * Thời gian cập nhật
   */
  @ApiProperty({
    description: 'Thời gian cập nhật (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  updatedAt: number;

  /**
   * Người cập nhật
   */
  @ApiPropertyOptional({
    description: 'ID người cập nhật',
    example: 1,
  })
  @Expose()
  updatedBy?: number;

  /**
   * Tên người tạo
   */
  @ApiPropertyOptional({
    description: 'Tên người tạo',
    example: 'John Doe',
  })
  @Expose()
  createdByName?: string | null;

  /**
   * Tên người cập nhật
   */
  @ApiPropertyOptional({
    description: 'Tên người cập nhật',
    example: 'Jane Smith',
  })
  @Expose()
  updatedByName?: string | null;

  /**
   * Thời gian xóa (epoch millis)
   */
  @ApiPropertyOptional({
    description: 'Thời gian xóa (epoch millis)',
    example: 1640995200000,
  })
  @Expose()
  deletedAt?: number | null;

  /**
   * Người xóa
   */
  @ApiPropertyOptional({
    description: 'ID người xóa',
    example: 1,
  })
  @Expose()
  deletedBy?: number | null;

  /**
   * Tên người xóa
   */
  @ApiPropertyOptional({
    description: 'Tên người xóa',
    example: 'Admin User',
  })
  @Expose()
  deletedByName?: string | null;
}
