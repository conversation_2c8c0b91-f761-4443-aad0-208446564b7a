import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNotEmpty, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ModelConfigDto } from '@modules/agent/admin/dto/common';
import { IsValidAdminModelConfig } from '@modules/agent/validators/model-config.validator';
import { ProviderEnumq } from '@/shared/services/ai/utils/type-provider.util';

/**
 * DTO cho việc tạo agent base mới
 */
export class CreateAgentBaseDto {
  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  /**
   * MIME type của avatar (nếu cần tạo avatar)
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar (nếu cần tạo avatar)',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình model AI
   */
  @ApiProperty({
    description: 'Cấu hình model AI',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsObject()
  modelConfig: ModelConfigDto;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example:
      'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction?: string | null;

  /**
   * ID của vector store
   */
  @ApiPropertyOptional({
    description: 'ID của vector store',
    example: 'vector-store-1',
  })
  @IsString()
  @IsOptional()
  vectorStoreId?: string;

  /**
   * Trạng thái active của agent base
   */
  @ApiPropertyOptional({
    description: 'Trạng thái active của agent base',
    example: true,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  active?: boolean;

  /**
   * Tên của base model (system model)
   */
  @ApiProperty({
    description: 'Tên của base model (system model)',
    example: 'gpt-3.5-turbo',
  })
  @IsString()
  modelName: string;

  /**
   * ID của system key LLM
   */
  @ApiPropertyOptional({
    description: 'ID của system key LLM',
    example: 'system-key-llm-uuid',
  })
  @IsString()
  @IsValidAdminModelConfig()
  keyLlmId: string;

  /**
   * ID của finetuning model
   */
  @ApiPropertyOptional({
    description: 'Nhà cung cấp AI',
    example: 'OPENAI',
  })
  @IsString()
  @IsNotEmpty()
  provider: string;
}
