import { AdminDataFineTune } from '../../entities/admin-data-fine-tune.entity';
import { AdminDataFineTuneResponseDto, AdminDataFineTuneDetailResponseDto } from '../dto/data-fine-tune';
import { EmployeeInfoSimpleDto } from '@/modules/employee/dto/employee-info-simple.dto';

/**
 * Mapper cho AdminDataFineTune
 * Chuyển đổi giữa entity và DTO
 */
export class AdminDataFineTuneMapper {
  /**
   * Chuyển đổi entity sang response DTO (không bao gồm dữ liệu training)
   * @param entity AdminDataFineTune entity
   * @returns AdminDataFineTuneResponseDto
   */
  static toResponseDto(
    entity: AdminDataFineTune,
  ): AdminDataFineTuneResponseDto {
    return {
      id: entity.id,
      name: entity.name,
      description: entity.description,
      status: entity.status,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    };
  }

  /**
   * Chuyển đổi entity sang detail response DTO với đầy đủ dữ liệu
   * @param entity AdminDataFineTune entity
   * @param trainDatasetUrl URL download cho train dataset
   * @param validDatasetUrl URL download cho valid dataset
   * @param createdEmployee Thông tin employee tạo
   * @param updatedEmployee Thông tin employee cập nhật
   * @returns AdminDataFineTuneDetailResponseDto
   */
  static toDetailResponseDto(
    entity: AdminDataFineTune,
    trainDatasetUrl: string | null,
    validDatasetUrl: string | null,
    createdEmployee: EmployeeInfoSimpleDto | null,
    updatedEmployee: EmployeeInfoSimpleDto | null
  ): AdminDataFineTuneDetailResponseDto {
    return {
      id: entity.id,
      name: entity.name,
      description: entity.description,
      status: entity.status,
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
      trainDataset: trainDatasetUrl,
      validDataset: validDatasetUrl,
      created: createdEmployee,
      updated: updatedEmployee
    };
  }

  /**
   * Chuyển đổi mảng entities sang mảng response DTOs
   * @param entities Mảng AdminDataFineTune entities
   * @returns Mảng AdminDataFineTuneResponseDto
   */
  static toResponseDtoArray(
    entities: AdminDataFineTune[],
  ): AdminDataFineTuneResponseDto[] {
    return entities.map(entity => {
      return this.toResponseDto(entity);
    });
  }
}
