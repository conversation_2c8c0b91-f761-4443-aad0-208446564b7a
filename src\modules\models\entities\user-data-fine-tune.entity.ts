import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ProviderFineTuneEnum } from '../constants/provider.enum';
import { DataFineTuneStatus } from '../constants/data-fine-tune-status.enum';

/**
 * Entity đại diện cho bảng user_data_fine_tune trong cơ sở dữ liệu
 * Lưu dữ liệu fine-tune mà người dùng tự cung cấp, bao gồm tập train và validation
 */
@Entity('user_data_fine_tune')
export class UserDataFineTune {
  /**
   * ID duy nhất của bộ dữ liệu fine-tune người dùng
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên của bộ dữ liệu fine-tune
   */
  @Column({ name: 'name', type: 'varchar', length: 255, nullable: false })
  name: string;

  /**
   * <PERSON><PERSON> tả nội dung bộ dữ liệu
   */
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string | null;

  @Column({
    name: 'status',
    type: 'enum',
    enum: DataFineTuneStatus,
    default: DataFineTuneStatus.PENDING,
  })
  status: DataFineTuneStatus;

  /**
   * Key S3 của tập dữ liệu huấn luyện
   */
  @Column({ name: 'train_dataset', type: 'varchar', length: 255, nullable: false })
  trainDataset: string;

  /**
   * Key S3 của tập dữ liệu validation (nếu có)
   */
  @Column({ name: 'valid_dataset', type: 'varchar', length: 255, nullable: true })
  validDataset: string | null;

  /**
   * ID người dùng sở hữu bộ dữ liệu
   */
  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number | null;

  /**
   * Thời gian tạo bản ghi
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Thời gian cập nhật cuối cùng
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;

  /**
   * Thời gian bị xóa mềm (nếu có)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;

  /**
   * Nhà cung cấp AI
   */
  @Column({
    name: 'provider',
    type: 'enum',
    enum: ProviderFineTuneEnum,
    default: ProviderFineTuneEnum.OPENAI,
  })
  provider: ProviderFineTuneEnum;

  /**
   * ID của user gốc (nếu là bản sao từ user khác)
   */
  @Column({ name: 'source_user_id', type: 'uuid', nullable: true })
  sourceUserId: string | null;

  /**
   * ID của admin gốc (nếu là bản sao từ admin)
   */
  @Column({ name: 'source_admin_id', type: 'uuid', nullable: true })
  sourceAdminId: string | null;
}
