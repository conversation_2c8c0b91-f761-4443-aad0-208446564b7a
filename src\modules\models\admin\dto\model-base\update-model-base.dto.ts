import { ProviderEnumq } from '@/shared/services/ai/utils/type-provider.util';
import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  <PERSON><PERSON>ength,
  Min
} from 'class-validator';

/**
 * DTO cho việc cập nhật model base
 */
export class UpdateModelBaseDto {
  /**
   * ID của system key LLM được sử dụng
   */
  @ApiPropertyOptional({
    description: 'ID của system key LLM được sử dụng',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  systemKeyLlmId?: string;

  /**
   * ID model từ nhà cung cấp
   */
  @ApiPropertyOptional({
    description: 'ID model từ nhà cung cấp',
    example: 'gpt-4-turbo-preview',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  modelId?: string;

  /**
   * Tên hiển thị của model
   */
  @ApiPropertyOptional({
    description: 'Tên hiển thị của model',
    example: 'GPT-4 Turbo Preview',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  /**
   * Mô tả về model
   */
  @ApiPropertyOptional({
    description: 'Mô tả về model',
    example: 'Model GPT-4 Turbo với khả năng xử lý văn bản và hình ảnh',
  })
  @IsOptional()
  @IsString()
  description?: string;

  /**
   * Nhà cung cấp AI
   */
  @ApiPropertyOptional({
    description: 'Nhà cung cấp AI',
    enum: ProviderEnumq,
    example: ProviderEnumq.OPENAI,
  })
  @IsOptional()
  @IsEnum(ProviderEnumq)
  provider?: ProviderEnumq;

  /**
   * Số token tối đa
   */
  @ApiPropertyOptional({
    description: 'Số token tối đa',
    example: 4096,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxTokens?: number;

  /**
   * Kích thước context window
   */
  @ApiPropertyOptional({
    description: 'Kích thước context window',
    example: 128000,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  contextWindow?: number;

  /**
   * Chi phí input per 1k tokens (USD)
   */
  @ApiPropertyOptional({
    description: 'Chi phí input per 1k tokens (USD)',
    example: 0.01,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  inputCostPer1kTokens?: number;

  /**
   * Chi phí output per 1k tokens (USD)
   */
  @ApiPropertyOptional({
    description: 'Chi phí output per 1k tokens (USD)',
    example: 0.03,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  outputCostPer1kTokens?: number;

  /**
   * Có cho phép user sử dụng không
   */
  @ApiPropertyOptional({
    description: 'Có cho phép user sử dụng không',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isUserAccessible?: boolean;

  /**
   * Có cho phép fine-tuning không
   */
  @ApiPropertyOptional({
    description: 'Có cho phép fine-tuning không',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isFineTunable?: boolean;

  /**
   * Metadata bổ sung
   */
  @ApiPropertyOptional({
    description: 'Metadata bổ sung (JSON)',
    example: { version: '1.1', features: ['chat', 'completion', 'vision'] },
  })
  @IsOptional()
  metadata?: any;
}
