-- Migration: Add order_status_enum type and update user_orders table
-- Date: 2024-01-XX
-- Description: Thêm enum order_status_enum và cập nhật bảng user_orders

-- Tạo enum order_status_enum nếu chưa tồn tại
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'order_status_enum') THEN
        CREATE TYPE order_status_enum AS ENUM (
            'pending',
            'confirmed', 
            'processing',
            'completed',
            'cancelled'
        );
    END IF;
END$$;

-- Thêm cột order_status vào bảng user_orders nếu chưa tồn tại
ALTER TABLE user_orders 
ADD COLUMN IF NOT EXISTS order_status order_status_enum;

-- Thêm comment cho cột order_status
COMMENT ON COLUMN user_orders.order_status 
IS 'Trạng thái đơn hàng (ENUM: pending, confirmed, processing, completed, cancelled)';

-- C<PERSON><PERSON> nhật dữ liệu hiện có với giá trị mặc định
UPDATE user_orders 
SET order_status = 'pending' 
WHERE order_status IS NULL;

-- Tạo index cho cột order_status để tối ưu truy vấn
CREATE INDEX IF NOT EXISTS idx_user_orders_order_status ON user_orders (order_status);

-- Tạo index cho cột shipping_status để tối ưu truy vấn
CREATE INDEX IF NOT EXISTS idx_user_orders_shipping_status ON user_orders (shipping_status);

-- Tạo index kết hợp cho user_id và order_status
CREATE INDEX IF NOT EXISTS idx_user_orders_user_id_order_status ON user_orders (user_id, order_status);

-- Tạo index kết hợp cho user_id và shipping_status
CREATE INDEX IF NOT EXISTS idx_user_orders_user_id_shipping_status ON user_orders (user_id, shipping_status);

-- Kiểm tra kết quả
SELECT 
    column_name, 
    data_type, 
    column_default, 
    is_nullable,
    udt_name
FROM information_schema.columns 
WHERE table_name = 'user_orders' 
AND column_name = 'order_status';

-- Hiển thị các giá trị enum có thể
SELECT enumlabel 
FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'order_status_enum')
ORDER BY enumsortorder;
