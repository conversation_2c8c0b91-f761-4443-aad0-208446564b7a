import { EmployeeInfoSimpleDto } from '@/modules/employee/dto/employee-info-simple.dto';
import { ProviderEnum } from '@/modules/models/constants';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SystemKeyLlmResponseDto {
  @ApiProperty({
    description: 'ID của system key',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  @ApiProperty({
    description: 'Tên định danh cho key',
    example: 'OpenAI Production Key'
  })
  name: string;

  @ApiProperty({
    description: 'Nhà cung cấp model',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI
  })
  provider: ProviderEnum;
}

/**
 * DTO response cho system key LLM
 */
export class SystemKeyLlmDetailResponseDto extends SystemKeyLlmResponseDto {
  @ApiProperty({
    description: 'Thờ<PERSON> gian tạo (epoch timestamp)',
    example: 1703980800000
  })
  createdAt: number;

  @ApiPropertyOptional({
    description: 'ID người tạo',
    example: {
      employeeId: 1,
      name: '<PERSON>',
      avatar: 'https://example.com/avatar.jpg',
      date: 1703980800000,
    },
    nullable: true
  })
  created: EmployeeInfoSimpleDto | null;

  @ApiProperty({
    description: 'Thời gian cập nhật (epoch timestamp)',
    example: 1703980800000
  })
  updatedAt: number;

  @ApiPropertyOptional({
    description: 'ID người cập nhật',
     example: {
      employeeId: 1,
      name: 'John Doe',
      avatar: 'https://example.com/avatar.jpg',
      date: 1703980800000,
    },
    nullable: true
  })
  updated: EmployeeInfoSimpleDto | null;
}